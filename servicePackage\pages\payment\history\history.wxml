<!--pages/payment/history/history.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 内容容器 -->
  <view class="content-container">
    <!-- 统计卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <view class="stats-value">¥{{totalPaid}}</view>
        <view class="stats-label">已缴总额</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">¥{{totalUnpaid}}</view>
        <view class="stats-label">待缴总额</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item {{totalOverdue > 0 ? 'overdue' : ''}}">
        <view class="stats-value">¥{{totalOverdue}}</view>
        <view class="stats-label">逾期总额</view>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <!-- 时间筛选 -->
      <view class="filter-group">
        <view class="filter-label">时间</view>
        <view class="filter-options">
          <view class="filter-option {{timeLevel === 2 ? 'active' : ''}}" bindtap="changeTimeFilter" data-level="2">本月</view>
          <view class="filter-option {{timeLevel === 3 ? 'active' : ''}}" bindtap="changeTimeFilter" data-level="3">本季度</view>
          <view class="filter-option {{timeLevel === 4 ? 'active' : ''}}" bindtap="changeTimeFilter" data-level="4">本年</view>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="filter-group">
        <view class="filter-label">状态</view>
        <view class="filter-options">
          <view class="filter-option {{statusFilter === 'all' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="all">全部</view>
          <view class="filter-option {{statusFilter === 'wait_pay' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="wait_pay">待付款</view>
          <view class="filter-option {{statusFilter === 'settle' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="settle">已付款</view>
        </view>
      </view>

      <!-- 重置筛选按钮 -->
      <view class="reset-filter" bindtap="resetFilters">
        <view class="reset-icon"></view>
        <text>重置筛选</text>
      </view>
    </view>

    <!-- 缴费记录列表 -->
    <view class="payment-records">
      <!-- 加载中状态 -->
      <view class="loading-container" wx:if="{{isLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{showEmpty && !isLoading}}">
        <view class="empty-icon"></view>
        <view class="empty-text">{{emptyText}}</view>
        <view class="empty-action" bindtap="handleEmptyAction">{{emptyActionText}}</view>
      </view>

      <!-- 记录列表 -->
      <block wx:if="{{!isLoading && paymentRecords.length > 0}}">
        <view class="payment-card" wx:for="{{paymentRecords}}" wx:key="id" bindtap="viewPaymentDetail" data-id="{{item.id}}">
          <!-- 核心信息：缴费名称 + 支付状态 -->
          <view class="payment-card-header">
            <view class="payment-title">{{getPaymentName(item)}}</view>
            <view class="payment-status {{item.status}}">
              {{getStatusText(item.status)}}
            </view>
          </view>

          <!-- 核心信息：账单时间 + 费用 + 支付周期 -->
          <view class="payment-card-body">
            <view class="payment-main-info">
              <!-- 账单时间 -->
              <view class="payment-date">
                <text class="label">账单时间：</text>
                <text class="value" wx:if="{{item.payTime}}">{{formatDate(item.payTime)}}</text>
                <text class="value" wx:elif="{{item.billDate}}">{{formatDate(item.billDate)}}</text>
                <text class="value" wx:else>--</text>
              </view>

              <!-- 支付周期 -->
              <view class="payment-cycle">
                <text class="label">支付周期：</text>
                <text class="value">{{getPaymentCycle(item)}}</text>
              </view>

              <!-- 支付方式（次要信息） -->
              <view class="payment-method" wx:if="{{item.payType}}">
                <text class="label">支付方式：</text>
                <text class="value">{{getPaymentTypeText(item.payType)}}</text>
              </view>
            </view>

            <!-- 费用信息 -->
            <view class="payment-amount">
              <view class="amount-value">¥{{formatAmount(item.payAmount)}}</view>
              <view class="amount-total" wx:if="{{item.totalAmount && item.totalAmount !== item.payAmount}}">
                <text class="amount-label">总额：</text>
                <text class="amount-original">¥{{formatAmount(item.totalAmount)}}</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="payment-card-footer" wx:if="{{item.status === 'wait_pay'}}">
            <view class="payment-action pay" catchtap="payNow" data-id="{{item.id}}">
              <text>立即缴费</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{hasMore}}">
          <view class="loading-spinner" wx:if="{{loadingMore}}"></view>
          <text wx:if="{{loadingMore}}">加载更多...</text>
          <text wx:else>上拉加载更多</text>
        </view>
        <view class="load-more" wx:else>
          <text>没有更多记录了</text>
        </view>
      </block>
    </view>
  </view>
</view>
