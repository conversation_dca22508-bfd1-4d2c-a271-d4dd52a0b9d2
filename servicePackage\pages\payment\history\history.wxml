<!--pages/payment/history/history.wxml-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 内容容器 -->
  <view class="content-container">
    <!-- 统计卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <view class="stats-value">¥{{totalPaid}}</view>
        <view class="stats-label">已缴总额</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">¥{{totalUnpaid}}</view>
        <view class="stats-label">待缴总额</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item {{totalOverdue > 0 ? 'overdue' : ''}}">
        <view class="stats-value">¥{{totalOverdue}}</view>
        <view class="stats-label">逾期总额</view>
      </view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-section">
      <!-- 时间筛选 -->
      <view class="filter-group">
        <view class="filter-label">时间</view>
        <view class="filter-options">
          <view class="filter-option {{timeLevel === 2 ? 'active' : ''}}" bindtap="changeTimeFilter" data-level="2">本月</view>
          <view class="filter-option {{timeLevel === 3 ? 'active' : ''}}" bindtap="changeTimeFilter" data-level="3">本季度</view>
          <view class="filter-option {{timeLevel === 4 ? 'active' : ''}}" bindtap="changeTimeFilter" data-level="4">本年</view>
        </view>
      </view>

      <!-- 状态筛选 -->
      <view class="filter-group">
        <view class="filter-label">状态</view>
        <view class="filter-options">
          <view class="filter-option {{statusFilter === 'all' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="all">全部</view>
          <view class="filter-option {{statusFilter === 'wait_pay' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="wait_pay">待付款</view>
          <view class="filter-option {{statusFilter === 'settle' ? 'active' : ''}}" bindtap="changeStatusFilter" data-filter="settle">已付款</view>
        </view>
      </view>

      <!-- 重置筛选按钮 -->
      <view class="reset-filter" bindtap="resetFilters">
        <view class="reset-icon"></view>
        <text>重置筛选</text>
      </view>
    </view>

    <!-- 缴费记录列表 -->
    <view class="payment-records">
      <!-- 加载中状态 -->
      <view class="loading-container" wx:if="{{isLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{showEmpty && !isLoading}}">
        <view class="empty-icon"></view>
        <view class="empty-text">{{emptyText}}</view>
        <view class="empty-action" bindtap="handleEmptyAction">{{emptyActionText}}</view>
      </view>

      <!-- 记录列表 -->
      <block wx:if="{{!isLoading && paymentRecords.length > 0}}">
        <view class="payment-card" wx:for="{{paymentRecords}}" wx:key="id" bindtap="viewPaymentDetail" data-id="{{item.id}}">
          <view class="payment-card-header">
            <view class="payment-title">{{getPaymentName(item)}}</view>
            <view class="payment-status {{item.status}}">
              {{getStatusText(item.status)}}
            </view>
          </view>
          <view class="payment-card-body">
            <view class="payment-info">
              <view class="payment-cycle">{{getPaymentCycle(item)}}</view>
              <view class="payment-date" wx:if="{{item.payTime}}">
                支付时间：{{formatDate(item.payTime)}}
              </view>
              <view class="payment-date" wx:elif="{{item.billDate}}">
                账单日期：{{formatDate(item.billDate)}}
              </view>
              <view class="payment-method" wx:if="{{item.payType}}">
                支付方式：{{item.payType === 'wechat_pay' ? '微信支付' : (item.payType === 'alipay' ? '支付宝' : item.payType)}}
              </view>
            </view>
            <view class="payment-amount">
              <view class="amount-value">¥{{formatAmount(item.payAmount)}}</view>
              <view class="amount-total" wx:if="{{item.totalAmount !== item.payAmount}}">
                总额：¥{{formatAmount(item.totalAmount)}}
              </view>
            </view>
          </view>
          <view class="payment-card-footer" wx:if="{{item.status === 'wait_pay'}}">
            <view class="payment-action pay" catchtap="payNow" data-id="{{item.id}}">
              <text>立即缴费</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{hasMore}}">
          <view class="loading-spinner" wx:if="{{loadingMore}}"></view>
          <text wx:if="{{loadingMore}}">加载更多...</text>
          <text wx:else>上拉加载更多</text>
        </view>
        <view class="load-more" wx:else>
          <text>没有更多记录了</text>
        </view>
      </block>
    </view>
  </view>
</view>
