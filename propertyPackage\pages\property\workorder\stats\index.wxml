<!-- pages/property/workorder/stats/index.wxml -->
<view class="container {{darkMode ? 'darkMode' : ''}}">

  <!-- 时间范围选择器 -->
  <view class="time-range-selector">
    <view
      class="time-range-option {{timeRange === 'week' ? 'active' : ''}}"
      bindtap="switchTimeRange"
      data-range="week"
    >
      本周
    </view>
    <view
      class="time-range-option {{timeRange === 'month' ? 'active' : ''}}"
      bindtap="switchTimeRange"
      data-range="month"
    >
      本月
    </view>
    <view
      class="time-range-option {{timeRange === 'quarter' ? 'active' : ''}}"
      bindtap="switchTimeRange"
      data-range="quarter"
    >
      本季度
    </view>
    <view
      class="time-range-option {{timeRange === 'year' ? 'active' : ''}}"
      bindtap="switchTimeRange"
      data-range="year"
    >
      本年度
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view
      class="tab-item {{activeTab === 'overview' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="overview"
    >
      <view class="tab-icon overview-icon"></view>
      <text class="tab-text">概览</text>
    </view>
    <view
      class="tab-item {{activeTab === 'type' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="type"
    >
      <view class="tab-icon type-icon"></view>
      <text class="tab-text">类型分布</text>
    </view>
    <view
      class="tab-item {{activeTab === 'efficiency' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="efficiency"
    >
      <view class="tab-icon efficiency-icon"></view>
      <text class="tab-text">处理时效</text>
    </view>

    <view
      class="tab-item {{activeTab === 'trend' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="trend"
    >
      <view class="tab-icon trend-icon"></view>
      <text class="tab-text">趋势分析</text>
    </view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 概览内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'overview' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">工单概览</text>
      </view>
      <view class="card-body">
        <view class="summary-grid">
          <view class="summary-item">
            <text class="summary-value">{{statistics.statusCounts.total}}</text>
            <text class="summary-label">工单总量</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.statusCounts.wait_process}}</text>
            <text class="summary-label">待处理</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.statusCounts.processing}}</text>
            <text class="summary-label">处理中</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.statusCounts.complete}}</text>
            <text class="summary-label">已完成</text>
          </view>
          <view class="summary-item">
            <text class="summary-value">{{statistics.statusCounts.cancel}}</text>
            <text class="summary-label">已取消</text>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">工单完成率</text>
      </view>
      <view class="card-body">
        <view class="progress-container">
          <view class="progress-item">
            <view class="progress-label">完成率</view>
            <view class="progress-bar">
              <view class="progress-fill completed" style="width: {{completionRate}}%;"></view>
            </view>
            <view class="progress-value">{{completionRate}}%</view>
          </view>
          <view class="progress-item">
            <view class="progress-label">处理中</view>
            <view class="progress-bar">
              <view class="progress-fill processing" style="width: {{processingRate}}%;"></view>
            </view>
            <view class="progress-value">{{processingRate}}%</view>
          </view>
          <view class="progress-item">
            <view class="progress-label">已取消</view>
            <view class="progress-bar">
              <view class="progress-fill cancelled" style="width: {{cancelRate}}%;"></view>
            </view>
            <view class="progress-value">{{cancelRate}}%</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 类型分布内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'type' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">工单类型分布</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- ECharts饼图 -->
          <view class="echarts-container">
            <ec-canvas
              id="typeDistributionChart"
              canvas-id="typeDistributionChart"
              ec="{{typeDistributionChart}}"
              class="echarts-canvas"
            ></ec-canvas>
          </view>

          <!-- 数据统计 -->
          <view class="type-statistics" wx:if="{{typeDistributionData.length > 0}}">
            <view class="stats-title">详细统计</view>
            <view class="stats-list">
              <view
                wx:for="{{typeDistributionData}}"
                wx:key="type"
                class="stats-item"
              >
                <view class="stats-label">{{item.name}}</view>
                <view class="stats-value">{{item.value}}单 ({{item.percentage}}%)</view>
              </view>
            </view>
          </view>

          <!-- 无数据提示 -->
          <view class="no-data" wx:if="{{typeDistributionData.length === 0}}">
            <text class="no-data-text">暂无类型分布数据</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 处理时效内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'efficiency' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">工单处理时效</text>
      </view>
      <view class="card-body">
        <view class="efficiency-metrics">
          <view class="efficiency-item">
            <view class="efficiency-icon response-icon"></view>
            <view class="efficiency-info">
              <view class="efficiency-label">平均响应时间</view>
              <view class="efficiency-value">{{statistics.timeEfficiency.avgCompleteTime  }}</view>
            </view>
          </view>
          <view class="efficiency-item">
            <view class="efficiency-icon processing-icon"></view>
            <view class="efficiency-info">
              <view class="efficiency-label">平均处理时间</view>
              <view class="efficiency-value">{{statistics.timeEfficiency.avgResponseTime}}</view>
            </view>
          </view>
          <view class="efficiency-item">
            <view class="efficiency-icon ontime-icon"></view>
            <view class="efficiency-info">
              <view class="efficiency-label">按时完成率</view>
              <view class="efficiency-value">{{statistics.timeEfficiency.onTimeCompleteRate}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 趋势分析内容 -->
  <view class="tab-content" wx:if="{{activeTab === 'trend' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">工单趋势分析</text>
      </view>
      <view class="card-body">
        <view class="chart-container">
          <!-- 柱状图 -->
          <view class="bar-chart">
            <view class="chart-y-axis">
              <text wx:for="{{[5, 4, 3, 2, 1, 0]}}" wx:key="index">{{item * 10}}</text>
            </view>
            <view class="chart-content">
              <view class="chart-bars">
                <view
                  wx:for="{{statistics.trend.data}}"
                  wx:key="index"
                  class="chart-bar"
                  style="height: {{item.value * 10}}rpx;"
                ></view>
              </view>
              <view class="chart-x-axis">
                <text
                  wx:for="{{statistics.trend.labels}}"
                  wx:key="index"
                  class="x-label"
                >
                  {{item}}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
