<!--居民详情页-->
<view class="container">


  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab {{activeTab === 'basic' ? 'active' : ''}}" bindtap="switchTab" data-tab="basic">基本信息</view>
    <view class="tab {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">房屋信息</view>
    <view class="tab {{activeTab === 'vehicle' ? 'active' : ''}}" bindtap="switchTab" data-tab="vehicle">车辆信息</view>
    <view class="tab {{activeTab === 'record' ? 'active' : ''}}" bindtap="switchTab" data-tab="record">操作记录</view>
  </view>

  <!-- 基本信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'basic'}}">
    <view class="detail-card">


      <view class="resident-header">
        <view class="resident-name">{{residentData.residentName || '未填写'}}</view>
      </view>

      <view class="info-list">
        <!-- 非编辑模式 -->
        <block wx:if="{{!isEditingBasic}}">
          <view class="info-item">
            <view class="info-label">姓名</view>
            <view class="info-value">{{residentData.residentName || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">手机号</view>
            <view class="info-value">{{residentData.phone || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <view class="info-value">{{residentData.genderText || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <view class="info-value">{{residentData.birthday || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">证件类型</view>
            <view class="info-value">{{residentData.certificateTypeText || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">证件号码</view>
            <view class="info-value">{{residentData.idCardNumber || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">籍贯</view>
            <view class="info-value">{{residentData.nativePlace || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">标签</view>
            <view class="info-value">
              <block wx:if="{{residentData.tagText}}">
                <view class="tags">
                  <view class="tag" wx:for="{{residentData.tagList}}" wx:key="*this" wx:for-item="tag">{{tag}}</view>
                </view>
              </block>
              <block wx:else>
                <text class="no-data">无</text>
              </block>
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">备注</view>
            <view class="info-value">{{residentData.note || '无'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">创建时间</view>
            <view class="info-value">{{residentData.formattedCreateTime || '未知'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">更新时间</view>
            <view class="info-value">{{residentData.formattedUpdateTime || '未知'}}</view>
          </view>
        </block>

        <!-- 编辑模式 -->
        <block wx:else>
          <view class="info-item">
            <view class="info-label">姓名</view>
            <input class="info-input" type="text" value="{{editBasicData.residentName}}" data-field="residentName" bindinput="onBasicEditInput" placeholder="请输入姓名" />
          </view>
          <view class="info-item">
            <view class="info-label">手机号</view>
            <input class="info-input" type="number" value="{{editBasicData.phone}}" data-field="phone" bindinput="onBasicEditInput" placeholder="请输入手机号" />
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <picker class="info-picker" mode="selector" range="{{genderDict}}" range-key="nameCn" value="{{editGenderIndex}}" bindchange="onGenderChange">
              <view class="picker-display">{{editBasicData.genderText || '请选择性别'}}</view>
            </picker>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <picker class="info-picker" mode="date" value="{{editBasicData.birthday}}" bindchange="onBirthdayChange">
              <view class="picker-display">{{editBasicData.birthday || '请选择出生日期'}}</view>
            </picker>
          </view>
          <view class="info-item">
            <view class="info-label">证件类型</view>
            <picker class="info-picker" mode="selector" range="{{certificateTypeDict}}" range-key="nameCn" value="{{editCertificateTypeIndex}}" bindchange="onCertificateTypeChange">
              <view class="picker-display">{{editBasicData.certificateTypeText || '请选择证件类型'}}</view>
            </picker>
          </view>
          <view class="info-item">
            <view class="info-label">证件号码</view>
            <input class="info-input" type="text" value="{{editBasicData.idCardNumber}}" data-field="idCardNumber" bindinput="onBasicEditInput" placeholder="请输入证件号码" />
          </view>
          <view class="info-item">
            <view class="info-label">籍贯</view>
            <input class="info-input" type="text" value="{{editBasicData.nativePlace}}" data-field="nativePlace" bindinput="onBasicEditInput" placeholder="请输入籍贯" />
          </view>
          <view class="info-item">
            <view class="info-label">标签</view>
            <view class="tag-edit-container">
              <view class="selected-tags" wx:if="{{editBasicData.selectedTags && editBasicData.selectedTags.length > 0}}">
                <view class="tag" wx:for="{{editBasicData.selectedTags}}" wx:key="nameEn">{{item.nameCn}}</view>
              </view>
              <button class="btn-select-tags" bindtap="showTagSelector">选择标签</button>
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">备注</view>
            <textarea class="info-textarea" value="{{editBasicData.note}}" data-field="note" bindinput="onBasicEditInput" placeholder="请输入备注信息"></textarea>
          </view>
        </block>
      </view>
    </view>

    <!-- 基本信息编辑按钮 -->
    <view class="edit-actions">
      <block wx:if="{{!isEditingBasic}}">
        <button class="btn-edit-main" bindtap="enterBasicEditMode">编辑</button>
      </block>
      <block wx:else>
        <button class="btn-cancel-main" bindtap="cancelBasicEdit">取消</button>
        <button class="btn-confirm-main" bindtap="saveBasicEdit" disabled="{{submitting}}">确定</button>
      </block>
    </view>
  </view>

  <!-- 房屋信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house'}}">
    <block wx:if="{{houseList && houseList.length > 0}}">
        <view class="house-list">
          <view class="house-item-card" wx:for="{{houseList}}" wx:key="id">
            <view class="item-card-header">
              <view class="item-card-title">{{item.fullAddress}}</view>
              <view class="item-card-actions">
                <button class="btn-edit-card" bindtap="editHouseItem" data-id="{{item.id}}">编辑</button>
              </view>
            </view>
            <view class="item-card-content">
              <view class="item-info-row">
                <view class="item-info-label">房间类型</view>
                <view class="item-info-value">{{item.roomTypeText || '未知'}}</view>
              </view>
              <view class="item-info-row">
                <view class="item-info-label">居民类型</view>
                <view class="item-info-value">{{item.residentTypeText || '未知'}}</view>
              </view>
              <view class="item-info-row">
                <view class="item-info-label">状态</view>
                <view class="item-info-value">{{item.statusText || '未知'}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.createTime}}">
                <view class="item-info-label">创建时间</view>
                <view class="item-info-value">{{item.createTime}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{houseHasMore && !houseLoading}}">
          <text bindtap="loadMoreHouse">加载更多房屋</text>
        </view>

        <!-- 加载中 -->
        <view class="loading-more" wx:if="{{houseLoading}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </block>

    <block wx:else>
      <view class="empty-state" wx:if="{{!houseLoading}}">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无关联房屋</view>
      </view>

      <!-- 首次加载 -->
      <view class="loading-state" wx:if="{{houseLoading && houseList.length === 0}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
    </block>
  </view>

  <!-- 车辆信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'vehicle'}}">
    <block wx:if="{{vehicleList && vehicleList.length > 0}}">
        <view class="vehicle-list">
          <view class="vehicle-item-card" wx:for="{{vehicleList}}" wx:key="id">
            <view class="item-card-header">
              <view class="item-card-title">{{item.plateNumber}}</view>
              <view class="item-card-actions">
                <button class="btn-edit-card" bindtap="editVehicleItem" data-id="{{item.id}}">编辑</button>
              </view>
            </view>
            <view class="item-card-content">
              <view class="item-info-row" wx:if="{{item.vehicleColor}}">
                <view class="item-info-label">车辆颜色</view>
                <view class="item-info-value">{{item.vehicleColor}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.parkingType}}">
                <view class="item-info-label">停车类型</view>
                <view class="item-info-value">{{item.parkingType}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.parkingNumber}}">
                <view class="item-info-label">车位号</view>
                <view class="item-info-value">{{item.parkingNumber}}</view>
              </view>
              <view class="item-info-row">
                <view class="item-info-label">状态</view>
                <view class="item-info-value">{{item.statusText || '未知'}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.formattedCreateTime}}">
                <view class="item-info-label">创建时间</view>
                <view class="item-info-value">{{item.formattedCreateTime}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.note}}">
                <view class="item-info-label">备注</view>
                <view class="item-info-value">{{item.note}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{vehicleHasMore && !vehicleLoading}}">
          <text bindtap="loadMoreVehicle">加载更多车辆</text>
        </view>

        <!-- 加载中 -->
        <view class="loading-more" wx:if="{{vehicleLoading}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </block>

    <block wx:else>
      <view class="empty-state" wx:if="{{!vehicleLoading}}">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无关联车辆</view>
      </view>

      <!-- 首次加载 -->
      <view class="loading-state" wx:if="{{vehicleLoading && vehicleList.length === 0}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
    </block>
  </view>

  <!-- 操作记录标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'record'}}">
    <view class="detail-card">
      <!-- 暂无数据提示 -->
      <view class="empty-state">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无操作记录，等待接口完善</view>
      </view>
    </view>
  </view>

  <!-- 标签选择弹窗 -->
  <view class="tag-modal {{showTagModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideTagModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择标签</text>
        <view class="modal-close" bindtap="hideTagModal">×</view>
      </view>
      <view class="modal-body">
        <view class="tag-list">
          <view class="tag-item {{item.selected ? 'selected' : ''}}" wx:for="{{residentTagDict}}" wx:key="nameEn" bindtap="toggleTag" data-index="{{index}}">
            <view class="tag-checkbox">
              <view class="checkbox {{item.selected ? 'checked' : ''}}"></view>
            </view>
            <view class="tag-name">{{item.nameCn}}</view>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideTagModal">取消</button>
        <button class="btn-confirm" bindtap="confirmTagSelection">确定</button>
      </view>
    </view>
  </view>

  <!-- 房产编辑弹窗 -->
  <view class="house-edit-modal {{showHouseEditModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="hideHouseEditModal"></view>
    <view class="modal-content house-edit-content">
      <view class="modal-header">
        <text class="modal-title">编辑房产信息</text>
        <view class="modal-close" bindtap="hideHouseEditModal">×</view>
      </view>
      <view class="modal-body">
        <view class="house-edit-form">
          <!-- 住户信息 -->
          <view class="form-section">
            <view class="section-title">住户信息</view>
            <view class="form-item">
              <view class="form-label">住户</view>
              <view class="form-value">{{editHouseData.residentName}} ({{editHouseData.phone}})</view>
            </view>
          </view>

          <!-- 楼栋选择 -->
          <view class="form-item">
            <view class="form-label required">楼栋</view>
            <picker class="form-picker" mode="selector" range="{{buildingList}}" range-key="buildingNumber" value="{{editBuildingIndex}}" bindchange="onBuildingChange">
              <view class="picker-display">{{editHouseData.buildingNumber || '请选择楼栋'}}</view>
            </picker>
          </view>

          <!-- 单元选择 -->
          <view class="form-item">
            <view class="form-label">单元</view>
            <picker class="form-picker" mode="selector" range="{{unitList}}" range-key="unitNumber" value="{{editUnitIndex}}" bindchange="onUnitChange">
              <view class="picker-display">{{editHouseData.unitNumber || '请选择单元（可选）'}}</view>
            </picker>
          </view>

          <!-- 房间选择 -->
          <view class="form-item">
            <view class="form-label required">房间</view>
            <picker class="form-picker" mode="selector" range="{{filteredRoomList || roomList}}" range-key="roomNumber" value="{{editRoomIndex}}" bindchange="onRoomChange">
              <view class="picker-display">{{editHouseData.roomNumber || '请选择房间'}}</view>
            </picker>
          </view>

          <!-- 完整地址 -->
          <view class="form-item">
            <view class="form-label">完整地址</view>
            <view class="form-value auto-address">{{editHouseData.fullAddress || '选择房间后自动生成'}}</view>
          </view>

          <!-- 住户类型 -->
          <view class="form-item">
            <view class="form-label required">住户类型</view>
            <picker class="form-picker" mode="selector" range="{{residentTypeDict}}" range-key="nameCn" value="{{editResidentTypeIndex}}" bindchange="onResidentTypeChange">
              <view class="picker-display">{{editHouseData.residentTypeText || '请选择住户类型'}}</view>
            </picker>
          </view>

          <!-- 房间户型 -->
          <view class="form-item">
            <view class="form-label">房间户型</view>
            <picker class="form-picker" mode="selector" range="{{roomTypeDict}}" range-key="nameCn" value="{{editRoomTypeIndex}}" bindchange="onRoomTypeChange">
              <view class="picker-display">{{editHouseData.roomTypeText || '请选择房间户型'}}</view>
            </picker>
          </view>

          <!-- 状态 -->
          <view class="form-item">
            <view class="form-label">状态</view>
            <picker class="form-picker" mode="selector" range="{{residentStatusDict}}" range-key="nameCn" value="{{editStatusIndex}}" bindchange="onStatusChange">
              <view class="picker-display">{{editHouseData.statusText || '请选择状态'}}</view>
            </picker>
          </view>

          <!-- 是否默认房产 -->
          <view class="form-item">
            <view class="form-label">默认房产</view>
            <switch class="form-switch" checked="{{editHouseData.isDefault}}" bindchange="onDefaultChange" />
          </view>

          <!-- 备注 -->
          <view class="form-item">
            <view class="form-label">备注</view>
            <textarea class="form-textarea" value="{{editHouseData.note}}" data-field="note" bindinput="onHouseEditInput" placeholder="请输入备注信息"></textarea>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideHouseEditModal">取消</button>
        <button class="btn-confirm" bindtap="saveHouseEdit" disabled="{{houseSubmitting}}">保存</button>
      </view>
    </view>
  </view>
</view>