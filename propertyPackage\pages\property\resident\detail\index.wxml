<!--居民详情页-->
<view class="container">


  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab {{activeTab === 'basic' ? 'active' : ''}}" bindtap="switchTab" data-tab="basic">基本信息</view>
    <view class="tab {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">房屋信息</view>
    <view class="tab {{activeTab === 'vehicle' ? 'active' : ''}}" bindtap="switchTab" data-tab="vehicle">车辆信息</view>
    <view class="tab {{activeTab === 'record' ? 'active' : ''}}" bindtap="switchTab" data-tab="record">操作记录</view>
  </view>

  <!-- 基本信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'basic'}}">
    <view class="detail-card">


      <view class="resident-header">
        <view class="resident-name">{{residentData.residentName}}</view>
        <view class="resident-badges">
          <view class="badge gender">{{residentData.genderText}}</view>
        </view>
      </view>

      <view class="info-list">
        <!-- 非编辑模式 -->
        <block wx:if="{{!isEditingBasic}}">
          <view class="info-item">
            <view class="info-label">姓名</view>
            <view class="info-value">{{residentData.residentName || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">手机号</view>
            <view class="info-value">{{residentData.phone || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <view class="info-value">{{residentData.genderText || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <view class="info-value">{{residentData.birthday || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">证件类型</view>
            <view class="info-value">{{residentData.certificateTypeText || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">证件号码</view>
            <view class="info-value">{{residentData.idCardNumber || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">籍贯</view>
            <view class="info-value">{{residentData.nativePlace || '未填写'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">标签</view>
            <view class="info-value">
              <block wx:if="{{residentData.tagText}}">
                <view class="tags">
                  <view class="tag" wx:for="{{residentData.tagList}}" wx:key="*this" wx:for-item="tag">{{tag}}</view>
                </view>
              </block>
              <block wx:else>
                <text class="no-data">无</text>
              </block>
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">备注</view>
            <view class="info-value">{{residentData.note || '无'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">创建时间</view>
            <view class="info-value">{{residentData.formattedCreateTime || '未知'}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">更新时间</view>
            <view class="info-value">{{residentData.formattedUpdateTime || '未知'}}</view>
          </view>
        </block>

        <!-- 编辑模式 -->
        <block wx:else>
          <view class="info-item">
            <view class="info-label">姓名</view>
            <input class="info-input" type="text" value="{{editBasicData.residentName}}" data-field="residentName" bindinput="onBasicEditInput" placeholder="请输入姓名" />
          </view>
          <view class="info-item">
            <view class="info-label">手机号</view>
            <input class="info-input" type="number" value="{{editBasicData.phone}}" data-field="phone" bindinput="onBasicEditInput" placeholder="请输入手机号" />
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <radio-group class="radio-group" data-field="gender" bindchange="onBasicEditInput">
              <label class="radio" wx:for="{{genderDict}}" wx:key="nameEn">
                <radio value="{{item.nameEn}}" checked="{{editBasicData.gender === item.nameEn}}"/>{{item.nameCn}}
              </label>
            </radio-group>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <picker mode="date" value="{{editBasicData.birthday}}" data-field="birthday" bindchange="onBasicEditInput">
              <view class="picker-value">{{editBasicData.birthday || '请选择出生日期'}}</view>
            </picker>
          </view>
          <view class="info-item">
            <view class="info-label">标签</view>
            <input class="info-input" type="text" value="{{editBasicData.tags}}" data-field="tags" bindinput="onBasicEditInput" placeholder="请输入标签，多个标签用逗号分隔" />
          </view>
          <view class="info-item">
            <view class="info-label">备注</view>
            <textarea class="info-textarea" value="{{editBasicData.note}}" data-field="note" bindinput="onBasicEditInput" placeholder="请输入备注信息"></textarea>
          </view>
        </block>
      </view>
    </view>

    <!-- 基本信息编辑按钮 -->
    <view class="edit-actions">
      <block wx:if="{{!isEditingBasic}}">
        <button class="btn-edit" bindtap="enterBasicEditMode">编辑基本信息</button>
      </block>
      <block wx:else>
        <button class="btn-cancel" bindtap="cancelBasicEdit">取消</button>
        <button class="btn-save" bindtap="saveBasicEdit" disabled="{{submitting}}">保存</button>
      </block>
    </view>
  </view>

  <!-- 房屋信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house'}}">
    <view class="detail-card">


      <block wx:if="{{houseList && houseList.length > 0}}">
        <view class="house-list">
          <view class="house-item-card" wx:for="{{houseList}}" wx:key="id">
            <view class="item-card-header">
              <view class="item-card-title">{{item.fullAddress}}</view>
              <button class="btn-edit-item" bindtap="editHouseItem" data-id="{{item.id}}">编辑</button>
            </view>
            <view class="item-card-content">
              <view class="item-info-row">
                <view class="item-info-label">房间类型</view>
                <view class="item-info-value">{{item.roomTypeText || '未知'}}</view>
              </view>
              <view class="item-info-row">
                <view class="item-info-label">居民类型</view>
                <view class="item-info-value">{{item.residentTypeText || '未知'}}</view>
              </view>
              <view class="item-info-row">
                <view class="item-info-label">状态</view>
                <view class="item-info-value">{{item.statusText || '未知'}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.createTime}}">
                <view class="item-info-label">创建时间</view>
                <view class="item-info-value">{{item.createTime}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{houseHasMore && !houseLoading}}">
          <text bindtap="loadMoreHouse">加载更多房屋</text>
        </view>

        <!-- 加载中 -->
        <view class="loading-more" wx:if="{{houseLoading}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state" wx:if="{{!houseLoading}}">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无关联房屋</view>
        </view>

        <!-- 首次加载 -->
        <view class="loading-state" wx:if="{{houseLoading && houseList.length === 0}}">
          <view class="loading-spinner"></view>
          <view class="loading-text">加载中...</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 车辆信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'vehicle'}}">
    <view class="detail-card">


      <block wx:if="{{vehicleList && vehicleList.length > 0}}">
        <view class="vehicle-list">
          <view class="vehicle-item-card" wx:for="{{vehicleList}}" wx:key="id">
            <view class="item-card-header">
              <view class="item-card-title">{{item.plateNumber}}</view>
              <button class="btn-edit-item" bindtap="editVehicleItem" data-id="{{item.id}}">编辑</button>
            </view>
            <view class="item-card-content">
              <view class="item-info-row" wx:if="{{item.vehicleColor}}">
                <view class="item-info-label">车辆颜色</view>
                <view class="item-info-value">{{item.vehicleColor}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.parkingType}}">
                <view class="item-info-label">停车类型</view>
                <view class="item-info-value">{{item.parkingType}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.parkingNumber}}">
                <view class="item-info-label">车位号</view>
                <view class="item-info-value">{{item.parkingNumber}}</view>
              </view>
              <view class="item-info-row">
                <view class="item-info-label">状态</view>
                <view class="item-info-value">{{item.statusText || '未知'}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.formattedCreateTime}}">
                <view class="item-info-label">创建时间</view>
                <view class="item-info-value">{{item.formattedCreateTime}}</view>
              </view>
              <view class="item-info-row" wx:if="{{item.note}}">
                <view class="item-info-label">备注</view>
                <view class="item-info-value">{{item.note}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{vehicleHasMore && !vehicleLoading}}">
          <text bindtap="loadMoreVehicle">加载更多车辆</text>
        </view>

        <!-- 加载中 -->
        <view class="loading-more" wx:if="{{vehicleLoading}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state" wx:if="{{!vehicleLoading}}">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无关联车辆</view>
        </view>

        <!-- 首次加载 -->
        <view class="loading-state" wx:if="{{vehicleLoading && vehicleList.length === 0}}">
          <view class="loading-spinner"></view>
          <view class="loading-text">加载中...</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 操作记录标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'record'}}">
    <view class="detail-card">


      <!-- 暂无数据提示 -->
      <view class="empty-state">
        <view class="empty-icon"></view>
        <view class="empty-text">暂无操作记录，等待接口完善</view>
      </view>
    </view>
  </view>
</view>