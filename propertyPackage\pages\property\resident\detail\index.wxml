<!--居民详情页-->
<view class="container">
  <!-- 顶部操作栏 -->
  <view class="action-bar">
    <block wx:if="{{!isEditingBasic}}">
      <button class="btn-edit" bindtap="enterBasicEditMode">编辑</button>
      <button class="btn-call" bindtap="makePhoneCall" wx:if="{{residentData.phone}}">拨打电话</button>
    </block>
    <block wx:else>
      <button class="btn-cancel" bindtap="cancelBasicEdit">取消</button>
      <button class="btn-save" bindtap="saveBasicEdit" disabled="{{submitting}}">保存</button>
    </block>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab {{activeTab === 'basic' ? 'active' : ''}}" bindtap="switchTab" data-tab="basic">基本信息</view>
    <view class="tab {{activeTab === 'house' ? 'active' : ''}}" bindtap="switchTab" data-tab="house">房屋信息</view>
    <view class="tab {{activeTab === 'vehicle' ? 'active' : ''}}" bindtap="switchTab" data-tab="vehicle">车辆信息</view>
  </view>

  <!-- 基本信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'basic'}}">
    <view class="detail-card">
      <view class="resident-header">
        <view class="resident-name">{{residentData.residentName}}</view>
        <view class="resident-badges">
          <view class="badge gender">{{residentData.genderText}}</view>
        </view>
      </view>

      <view class="info-list">
        <!-- 非编辑模式 -->
        <block wx:if="{{!isEditingBasic}}">
          <view class="info-item">
            <view class="info-label">手机号</view>
            <view class="info-value">{{residentData.phone}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">证件类型</view>
            <view class="info-value">{{residentData.certificateTypeText}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">证件号码</view>
            <view class="info-value">{{residentData.idCardNumber}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <view class="info-value">{{residentData.genderText}}</view>
          </view>
          <view class="info-item" wx:if="{{residentData.birthday}}">
            <view class="info-label">出生日期</view>
            <view class="info-value">{{residentData.birthday}}</view>
          </view>
          <view class="info-item" wx:if="{{residentData.tagText}}">
            <view class="info-label">标签</view>
            <view class="info-value">{{residentData.tagText}}</view>
          </view>
          <view class="info-item" wx:if="{{residentData.note}}">
            <view class="info-label">备注</view>
            <view class="info-value">{{residentData.note}}</view>
          </view>
          <view class="info-item" wx:if="{{residentData.formattedCreateTime}}">
            <view class="info-label">创建时间</view>
            <view class="info-value">{{residentData.formattedCreateTime}}</view>
          </view>
          <view class="info-item" wx:if="{{residentData.formattedUpdateTime}}">
            <view class="info-label">更新时间</view>
            <view class="info-value">{{residentData.formattedUpdateTime}}</view>
          </view>
        </block>

        <!-- 编辑模式 -->
        <block wx:else>
          <view class="info-item">
            <view class="info-label">姓名</view>
            <input class="info-input" type="text" value="{{editBasicData.residentName}}" data-field="residentName" bindinput="onBasicEditInput" placeholder="请输入姓名" />
          </view>
          <view class="info-item">
            <view class="info-label">手机号</view>
            <input class="info-input" type="number" value="{{editBasicData.phone}}" data-field="phone" bindinput="onBasicEditInput" placeholder="请输入手机号" />
          </view>
          <view class="info-item">
            <view class="info-label">性别</view>
            <radio-group class="radio-group" data-field="gender" bindchange="onBasicEditInput">
              <label class="radio" wx:for="{{genderDict}}" wx:key="nameEn">
                <radio value="{{item.nameEn}}" checked="{{editBasicData.gender === item.nameEn}}"/>{{item.nameCn}}
              </label>
            </radio-group>
          </view>
          <view class="info-item">
            <view class="info-label">出生日期</view>
            <picker mode="date" value="{{editBasicData.birthday}}" data-field="birthday" bindchange="onBasicEditInput">
              <view class="picker-value">{{editBasicData.birthday || '请选择出生日期'}}</view>
            </picker>
          </view>
          <view class="info-item">
            <view class="info-label">标签</view>
            <input class="info-input" type="text" value="{{editBasicData.tags}}" data-field="tags" bindinput="onBasicEditInput" placeholder="请输入标签，多个标签用逗号分隔" />
          </view>
          <view class="info-item">
            <view class="info-label">备注</view>
            <textarea class="info-textarea" value="{{editBasicData.note}}" data-field="note" bindinput="onBasicEditInput" placeholder="请输入备注信息"></textarea>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 房屋信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'house'}}">
    <view class="detail-card">
      <view class="card-header">
        <view class="card-title">关联房屋</view>
      </view>

      <block wx:if="{{houseList && houseList.length > 0}}">
        <view class="house-list">
          <view class="house-item" wx:for="{{houseList}}" wx:key="id">
            <view class="house-info">
              <view class="house-address">{{item.fullAddress}}</view>
              <view class="house-details">
                <text>{{item.roomTypeText}} | {{item.residentTypeText}}</text>
                <text class="house-status">{{item.statusText}}</text>
              </view>
              <view class="house-date" wx:if="{{item.createTime}}">创建时间: {{item.createTime}}</view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{houseHasMore && !houseLoading}}">
          <text bindtap="loadMoreHouse">加载更多房屋</text>
        </view>

        <!-- 加载中 -->
        <view class="loading-more" wx:if="{{houseLoading}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state" wx:if="{{!houseLoading}}">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无关联房屋</view>
        </view>

        <!-- 首次加载 -->
        <view class="loading-state" wx:if="{{houseLoading && houseList.length === 0}}">
          <view class="loading-spinner"></view>
          <view class="loading-text">加载中...</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 车辆信息标签页 -->
  <view class="tab-content" wx:if="{{activeTab === 'vehicle'}}">
    <view class="detail-card">
      <view class="card-header">
        <view class="card-title">关联车辆</view>
      </view>

      <block wx:if="{{vehicleList && vehicleList.length > 0}}">
        <view class="vehicle-list">
          <view class="vehicle-item" wx:for="{{vehicleList}}" wx:key="id">
            <view class="vehicle-info">
              <view class="vehicle-plate">{{item.plateNumber}}</view>
              <view class="vehicle-details">
                <text wx:if="{{item.brand}}">{{item.brand}}</text>
                <text wx:if="{{item.model}}"> {{item.model}}</text>
                <text wx:if="{{item.color}}"> | {{item.color}}</text>
              </view>
              <view class="vehicle-status">状态: {{item.statusText}}</view>
              <view class="vehicle-date" wx:if="{{item.formattedCreateTime}}">创建时间: {{item.formattedCreateTime}}</view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{vehicleHasMore && !vehicleLoading}}">
          <text bindtap="loadMoreVehicle">加载更多车辆</text>
        </view>

        <!-- 加载中 -->
        <view class="loading-more" wx:if="{{vehicleLoading}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </block>

      <block wx:else>
        <view class="empty-state" wx:if="{{!vehicleLoading}}">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无关联车辆</view>
        </view>

        <!-- 首次加载 -->
        <view class="loading-state" wx:if="{{vehicleLoading && vehicleList.length === 0}}">
          <view class="loading-spinner"></view>
          <view class="loading-text">加载中...</view>
        </view>
      </block>
    </view>
  </view>


</view>