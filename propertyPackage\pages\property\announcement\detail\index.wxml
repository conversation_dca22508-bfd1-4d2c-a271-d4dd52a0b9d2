<!-- 公告详情页 -->
<view class="container {{darkMode ? 'dark-mode' : ''}}">
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 公告详情 -->
  <view class="announcement-detail" wx:if="{{!isLoading && announcement}}">
    <!-- 公告标题 -->
    <view class="announcement-title">{{announcement.title}}</view>

    <!-- 公告信息 -->
    <view class="announcement-info">
      <view class="info-item">
        <view class="type-icon {{announcement.type}}"></view>
        <text>{{announcement.typeText}}</text>
      </view>
      <view class="info-item">
        <view class="time-icon"></view>
        <text>{{announcement.publishTime}}</text>
      </view>
      <view class="info-item" wx:if="{{announcement.readCount !== undefined}}">
        <view class="read-icon"></view>
        <text>{{announcement.readCount}} 阅读</text>
      </view>
    </view>

    <!-- 公告内容 -->
    <view class="announcement-content">
      <rich-text nodes="{{announcement.content}}"></rich-text>
    </view>

    <!-- 图片展示 -->
    <view class="image-gallery" wx:if="{{announcement.images && announcement.images.length > 0}}">
      <view class="image-header">
        <text class="image-count">图片 ({{announcement.images.length}})</text>
      </view>
      <view class="image-grid">
        <view class="image-item" wx:for="{{announcement.images}}" wx:key="index" bindtap="previewImage" data-index="{{index}}">
          <image src="{{item}}" mode="aspectFill" class="image" lazy-load="{{true}}" binderror="onImageError" data-index="{{index}}"></image>
          <!-- 如果图片超过9张，在第9张显示更多提示 -->
          <view class="more-overlay" wx:if="{{index === 8 && announcement.images.length > 9}}">
            <text class="more-text">+{{announcement.images.length - 9}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 预览模式提示 -->
    <view class="preview-tip" wx:if="{{isPreview}}">
      <text class="tip-text">预览模式</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{!isLoading && !announcement}}">
    <view class="error-icon"></view>
    <view class="error-text">加载失败，请重试</view>
    <view class="error-btn" bindtap="onBackTap">返回</view>
  </view>
</view>
