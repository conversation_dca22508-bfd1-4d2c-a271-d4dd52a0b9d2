// pages/property/resident/detail/index.js
const util = require('@/utils/util.js')
const propertyApi = require('@/api/propertyApi.js')
const communityApi = require('@/api/communityApi.js')
Page({
  data: {
    id: null, // 居民ID
    residentData: {}, // 居民基本信息
    activeTab: 'basic', // 当前激活的标签：basic, house, vehicle

    // 房屋信息相关
    houseList: [],
    housePageNum: 1,
    housePageSize: 10,
    houseHasMore: true,
    houseLoading: false,

    // 车辆信息相关
    vehicleList: [],
    vehiclePageNum: 1,
    vehiclePageSize: 10,
    vehicleHasMore: true,
    vehicleLoading: false,

    // 字典数据
    residentTypeDict: [], // 居民类型字典
    genderDict: [], // 性别字典
    certificateTypeDict: [], // 证件类型字典
    roomTypeDict: [], // 房间类型字典
    residentStatusDict: [], // 居民状态字典
    residentTagDict: [], // 居民标签字典

    // 编辑相关
    isEditingBasic: false,
    editBasicData: {},
    submitting: false
  },

  onLoad: function(options) {
    const { id } = options;

    this.setData({
      id: id
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民详情'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载居民详情
    this.loadResidentDetail();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 按照MD文件要求初始化字典
      // 人员标签字典
      const residentTagDict = util.getDictByNameEn('resident_tag')[0].children;
      // 证件类型字典
      const certificateTypeDict = util.getDictByNameEn('certificate_type')[0].children;
      // 居民住户类型字典
      const residentTypeDict = util.getDictByNameEn('resident_type')[0].children;
      // 性别字典 - 注意MD文件中写错了，应该是gender
      const genderDict = util.getDictByNameEn('gender')[0].children;
      // 房间户型字典
      const roomTypeDict = util.getDictByNameEn('room_type')[0].children;
      // 房产审核状态字典
      const residentStatusDict = util.getDictByNameEn('resident_status')[0].children;

      this.setData({
        residentTagDict,
        certificateTypeDict,
        residentTypeDict,
        genderDict,
        roomTypeDict,
        residentStatusDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载居民详情
  loadResidentDetail: function() {
    if (!this.data.id) {
      wx.showToast({
        title: '居民ID不能为空',
        icon: 'none'
      });
      return;
    }

    propertyApi.getResidentDetail(this.data.id).then(res => {
      console.log('居民详情响应:', res);

      // 处理居民数据
      const residentData = this.processResidentData(res);

      this.setData({
        residentData: residentData,
        editBasicData: JSON.parse(JSON.stringify(residentData))
      });

      // 根据当前标签页加载对应数据
      if (this.data.activeTab === 'house') {
        this.loadHouseList();
      } else if (this.data.activeTab === 'vehicle') {
        this.loadVehicleList();
      }
    }).catch(error => {
      console.error('加载居民详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  // 处理居民数据
  processResidentData: function(resident) {
    // 性别映射
    const genderItem = this.data.genderDict.find(item => item.nameEn === resident.gender);
    resident.genderText = genderItem ? genderItem.nameCn : resident.gender;

    // 证件类型映射
    const certificateItem = this.data.certificateTypeDict.find(item => item.nameEn === resident.certificateType);
    resident.certificateTypeText = certificateItem ? certificateItem.nameCn : resident.certificateType;

    // 处理标签
    if (resident.tags) {
      const tagArray = resident.tags.split(',');
      resident.tagList = tagArray.map(tagEn => {
        const tagItem = this.data.residentTagDict.find(item => item.nameEn === tagEn);
        return tagItem ? tagItem.nameCn : tagEn;
      });
      resident.tagText = resident.tagList.join('、');
    } else {
      resident.tagList = [];
      resident.tagText = '无';
    }

    // 格式化时间
    if (resident.createTime) {
      resident.formattedCreateTime = util.formatTime(new Date(resident.createTime));
    }
    if (resident.updateTime) {
      resident.formattedUpdateTime = util.formatTime(new Date(resident.updateTime));
    }

    return resident;
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });

    // 根据标签页加载对应数据
    if (tab === 'house' && this.data.houseList.length === 0) {
      this.loadHouseList();
    } else if (tab === 'vehicle' && this.data.vehicleList.length === 0) {
      this.loadVehicleList();
    }
  },

  // 加载房屋列表
  loadHouseList: function(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        housePageNum: 1,
        houseList: [],
        houseHasMore: true
      });
    }

    this.setData({ houseLoading: true });

    const params = {
      pageNum: this.data.housePageNum,
      pageSize: this.data.housePageSize,
      residentId: this.data.id
    };

    propertyApi.propertyGetResidentRoomList(params).then(res => {
      console.log('房屋列表响应:', res);

      const newHouseList = res.list || [];
      // 处理房屋数据
      const processedHouseList = newHouseList.map(house => this.processHouseData(house));

      let allHouseList = [];
      if (isRefresh || this.data.housePageNum === 1) {
        allHouseList = processedHouseList;
      } else {
        allHouseList = [...this.data.houseList, ...processedHouseList];
      }

      this.setData({
        houseList: allHouseList,
        houseHasMore: res.hasNextPage || false,
        houseLoading: false
      });
    }).catch(error => {
      console.error('加载房屋列表失败:', error);
      this.setData({ houseLoading: false });
      wx.showToast({
        title: '加载房屋信息失败',
        icon: 'none'
      });
    });
  },

  // 处理房屋数据
  processHouseData: function(house) {
    // 房间类型映射
    const roomTypeItem = this.data.roomTypeDict.find(item => item.nameEn === house.roomType);
    house.roomTypeText = roomTypeItem ? roomTypeItem.nameCn : house.roomType;

    // 居民类型映射
    const residentTypeItem = this.data.residentTypeDict.find(item => item.nameEn === house.residentType);
    house.residentTypeText = residentTypeItem ? residentTypeItem.nameCn : house.residentType;

    // 状态映射
    const statusItem = this.data.residentStatusDict.find(item => item.nameEn === house.status);
    house.statusText = statusItem ? statusItem.nameCn : house.status;

    // 构建完整地址
    house.fullAddress = house.buildingNumber + (house.unitNumber ? house.unitNumber : '') + house.roomNumber;

    return house;
  },

  // 加载车辆列表
  loadVehicleList: function(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        vehiclePageNum: 1,
        vehicleList: [],
        vehicleHasMore: true
      });
    }

    this.setData({ vehicleLoading: true });

    const params = {
      pageNum: this.data.vehiclePageNum,
      pageSize: this.data.vehiclePageSize,
      residentId: this.data.id,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    propertyApi.propertyGetResidentVehicleList(params).then(res => {
      console.log('车辆列表响应:', res);

      const newVehicleList = res.list || [];
      // 处理车辆数据
      const processedVehicleList = newVehicleList.map(vehicle => this.processVehicleData(vehicle));

      let allVehicleList = [];
      if (isRefresh || this.data.vehiclePageNum === 1) {
        allVehicleList = processedVehicleList;
      } else {
        allVehicleList = [...this.data.vehicleList, ...processedVehicleList];
      }

      this.setData({
        vehicleList: allVehicleList,
        vehicleHasMore: res.hasNextPage || false,
        vehicleLoading: false
      });
    }).catch(error => {
      console.error('加载车辆列表失败:', error);
      this.setData({ vehicleLoading: false });
      wx.showToast({
        title: '加载车辆信息失败',
        icon: 'none'
      });
    });
  },

  // 处理车辆数据
  processVehicleData: function(vehicle) {
    // 状态映射 - 使用vehicle_status字典
    try {
      const vehicleStatusDict = util.getDictByNameEn('vehicle_status')[0].children;
      const statusItem = vehicleStatusDict.find(item => item.nameEn === vehicle.status);
      vehicle.statusText = statusItem ? statusItem.nameCn : vehicle.status;
    } catch (error) {
      vehicle.statusText = vehicle.status;
    }

    // 格式化时间
    if (vehicle.createTime) {
      vehicle.formattedCreateTime = util.formatTime(new Date(vehicle.createTime));
    }
    if (vehicle.updateTime) {
      vehicle.formattedUpdateTime = util.formatTime(new Date(vehicle.updateTime));
    }

    return vehicle;
  },

  // 进入基本信息编辑模式
  enterBasicEditMode: function() {
    this.setData({
      isEditingBasic: true
    });
  },

  // 取消基本信息编辑
  cancelBasicEdit: function() {
    this.setData({
      isEditingBasic: false,
      editBasicData: JSON.parse(JSON.stringify(this.data.residentData)) // 重置编辑数据
    });
  },

  // 保存基本信息编辑
  saveBasicEdit: function() {
    const editData = this.data.editBasicData;

    // 验证必填字段
    if (!editData.residentName || !editData.phone) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    // 准备提交数据
    const submitData = {
      id: this.data.id,
      residentName: editData.residentName,
      phone: editData.phone,
      gender: editData.gender,
      birthday: editData.birthday,
      note: editData.note || '',
      tags: editData.tags || ''
    };

    propertyApi.editResident(submitData).then(res => {
      console.log('编辑居民信息成功:', res);

      // 重新加载居民详情
      this.loadResidentDetail();

      this.setData({
        isEditingBasic: false,
        submitting: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }).catch(error => {
      console.error('保存居民信息失败:', error);
      this.setData({ submitting: false });
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    });
  },

  // 基本信息编辑输入
  onBasicEditInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const editBasicData = this.data.editBasicData;

    // 更新对应字段
    editBasicData[field] = value;

    this.setData({
      editBasicData: editBasicData
    });
  },

  // 房屋列表加载更多
  loadMoreHouse: function() {
    if (this.data.houseHasMore && !this.data.houseLoading) {
      this.setData({
        housePageNum: this.data.housePageNum + 1
      });
      this.loadHouseList();
    }
  },

  // 车辆列表加载更多
  loadMoreVehicle: function() {
    if (this.data.vehicleHasMore && !this.data.vehicleLoading) {
      this.setData({
        vehiclePageNum: this.data.vehiclePageNum + 1
      });
      this.loadVehicleList();
    }
  },

  // 拨打电话
  makePhoneCall: function() {
    if (!this.data.residentData.phone) {
      wx.showToast({
        title: '手机号不存在',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: this.data.residentData.phone,
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },



  // 查看房屋详情
  viewHouseDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/property/house/detail/index?id=${id}`
    });
  },

  // 查看车辆详情
  viewVehicleDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/vehicle/detail/index?id=${id}`
    });
  },

  // 添加房屋
  addHouse: function() {
    wx.navigateTo({
      url: `/propertyPackage/pages/property/house/add/index?residentId=${this.data.id}`
    });
  },

  // 添加车辆
  addVehicle: function() {
    wx.navigateTo({
      url: `/propertyPackage/pages/property/vehicle/add/index?residentId=${this.data.id}`
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载居民详情
    this.loadResidentDetail();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 编辑房屋信息
  editHouseItem: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/house/edit/index?id=${id}&residentId=${this.data.id}`
    });
  },

  // 编辑车辆信息
  editVehicleItem: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/vehicle/edit/index?id=${id}&residentId=${this.data.id}`
    });
  },

  // 编辑房屋信息
  editHouseItem: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: '房屋编辑功能开发中',
      icon: 'none'
    });
  },

  // 编辑车辆信息
  editVehicleItem: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: '车辆编辑功能开发中',
      icon: 'none'
    });
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})