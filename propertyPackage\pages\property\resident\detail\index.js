// pages/property/resident/detail/index.js
const util = require('@/utils/util.js')
const dateUtil = require('@/utils/dateUtil.js')
const propertyApi = require('@/api/propertyApi.js')
const communityApi = require('@/api/communityApi.js')
Page({
  data: {
    id: null, // 居民ID
    residentData: {}, // 居民基本信息
    activeTab: 'basic', // 当前激活的标签：basic, house, vehicle

    // 房屋信息相关
    houseList: [],
    housePageNum: 1,
    housePageSize: 10,
    houseHasMore: true,
    houseLoading: false,

    // 车辆信息相关
    vehicleList: [],
    vehiclePageNum: 1,
    vehiclePageSize: 10,
    vehicleHasMore: true,
    vehicleLoading: false,

    // 字典数据
    residentTypeDict: [], // 居民类型字典
    genderDict: [], // 性别字典
    certificateTypeDict: [], // 证件类型字典
    roomTypeDict: [], // 房间类型字典
    residentStatusDict: [], // 居民状态字典
    residentTagDict: [], // 居民标签字典

    // 编辑相关
    isEditingBasic: false,
    editBasicData: {},
    submitting: false,
    editGenderIndex: -1,
    editCertificateTypeIndex: -1,
    showTagModal: false,

    // 房产编辑相关
    showHouseEditModal: false,
    editHouseData: {},
    houseSubmitting: false,
    buildingList: [],
    unitList: [],
    roomList: [],
    editBuildingIndex: -1,
    editUnitIndex: -1,
    editRoomIndex: -1,
    editResidentTypeIndex: -1,
    editRoomTypeIndex: -1,
    editStatusIndex: -1
  },

  onLoad: function(options) {
    const { id } = options;

    this.setData({
      id: id
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民详情'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载居民详情
    this.loadResidentDetail();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 按照MD文件要求初始化字典
      // 人员标签字典
      const residentTagDict = util.getDictByNameEn('resident_tag')[0].children;
      // 证件类型字典
      const certificateTypeDict = util.getDictByNameEn('certificate_type')[0].children;
      // 居民住户类型字典
      const residentTypeDict = util.getDictByNameEn('resident_type')[0].children;
      // 性别字典 - 注意MD文件中写错了，应该是gender
      const genderDict = util.getDictByNameEn('gender')[0].children;
      // 房间户型字典
      const roomTypeDict = util.getDictByNameEn('room_type')[0].children;
      // 房产审核状态字典
      const residentStatusDict = util.getDictByNameEn('resident_status')[0].children;

      this.setData({
        residentTagDict,
        certificateTypeDict,
        residentTypeDict,
        genderDict,
        roomTypeDict,
        residentStatusDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载居民详情
  loadResidentDetail: function() {
    if (!this.data.id) {
      wx.showToast({
        title: '居民ID不能为空',
        icon: 'none'
      });
      return;
    }

    propertyApi.getResidentDetail(this.data.id).then(res => {
      console.log('居民详情响应:', res);

      // 处理居民数据
      const residentData = this.processResidentData(res);

      this.setData({
        residentData: residentData,
        editBasicData: JSON.parse(JSON.stringify(residentData))
      });

      // 根据当前标签页加载对应数据
      if (this.data.activeTab === 'house') {
        this.loadHouseList();
      } else if (this.data.activeTab === 'vehicle') {
        this.loadVehicleList();
      }
    }).catch(error => {
      console.error('加载居民详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  // 处理居民数据
  processResidentData: function(resident) {
    // 性别映射
    const genderItem = this.data.genderDict.find(item => item.nameEn === resident.gender);
    resident.genderText = genderItem ? genderItem.nameCn : resident.gender;

    // 证件类型映射
    const certificateItem = this.data.certificateTypeDict.find(item => item.nameEn === resident.certificateType);
    resident.certificateTypeText = certificateItem ? certificateItem.nameCn : resident.certificateType;

    // 处理标签
    if (resident.tags) {
      console.log('原始标签数据:', resident.tags);
      console.log('标签字典:', this.data.residentTagDict);
      const tagArray = resident.tags.split(',');
      resident.tagList = tagArray.map(tagEn => {
        const tagItem = this.data.residentTagDict.find(item => item.nameEn === tagEn.trim());
        return tagItem ? tagItem.nameCn : tagEn.trim();
      });
      resident.tagText = resident.tagList.join('、');
      console.log('处理后标签:', resident.tagList, resident.tagText);
    } else {
      resident.tagList = [];
      resident.tagText = '';
    }

    // 格式化时间
    if (resident.createTime) {
      resident.formattedCreateTime = dateUtil.formatTime(new Date(resident.createTime));
    }
    if (resident.updateTime) {
      resident.formattedUpdateTime = dateUtil.formatTime(new Date(resident.updateTime));
    }

    return resident;
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });

    // 根据标签页加载对应数据
    if (tab === 'house' && this.data.houseList.length === 0) {
      this.loadHouseList();
    } else if (tab === 'vehicle' && this.data.vehicleList.length === 0) {
      this.loadVehicleList();
    }
  },

  // 加载房屋列表
  loadHouseList: function(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        housePageNum: 1,
        houseList: [],
        houseHasMore: true
      });
    }

    this.setData({ houseLoading: true });

    const params = {
      pageNum: this.data.housePageNum,
      pageSize: this.data.housePageSize,
      residentId: this.data.id
    };

    propertyApi.propertyGetResidentRoomList(params).then(res => {
      console.log('房屋列表响应:', res);

      const newHouseList = res.list || [];
      // 处理房屋数据
      const processedHouseList = newHouseList.map(house => this.processHouseData(house));

      let allHouseList = [];
      if (isRefresh || this.data.housePageNum === 1) {
        allHouseList = processedHouseList;
      } else {
        allHouseList = [...this.data.houseList, ...processedHouseList];
      }

      this.setData({
        houseList: allHouseList,
        houseHasMore: res.hasNextPage || false,
        houseLoading: false
      });
    }).catch(error => {
      console.error('加载房屋列表失败:', error);
      this.setData({ houseLoading: false });
      wx.showToast({
        title: '加载房屋信息失败',
        icon: 'none'
      });
    });
  },

  // 处理房屋数据
  processHouseData: function(house) {
    // 房间类型映射
    const roomTypeItem = this.data.roomTypeDict.find(item => item.nameEn === house.roomType);
    house.roomTypeText = roomTypeItem ? roomTypeItem.nameCn : house.roomType;

    // 居民类型映射
    const residentTypeItem = this.data.residentTypeDict.find(item => item.nameEn === house.residentType);
    house.residentTypeText = residentTypeItem ? residentTypeItem.nameCn : house.residentType;

    // 状态映射
    const statusItem = this.data.residentStatusDict.find(item => item.nameEn === house.status);
    house.statusText = statusItem ? statusItem.nameCn : house.status;

    // 构建完整地址
    house.fullAddress = house.buildingNumber + (house.unitNumber ? house.unitNumber : '') + house.roomNumber;

    return house;
  },

  // 加载车辆列表
  loadVehicleList: function(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        vehiclePageNum: 1,
        vehicleList: [],
        vehicleHasMore: true
      });
    }

    this.setData({ vehicleLoading: true });

    const params = {
      pageNum: this.data.vehiclePageNum,
      pageSize: this.data.vehiclePageSize,
      residentId: this.data.id,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    propertyApi.propertyGetResidentVehicleList(params).then(res => {
      console.log('车辆列表响应:', res);

      const newVehicleList = res.list || [];
      // 处理车辆数据
      const processedVehicleList = newVehicleList.map(vehicle => this.processVehicleData(vehicle));

      let allVehicleList = [];
      if (isRefresh || this.data.vehiclePageNum === 1) {
        allVehicleList = processedVehicleList;
      } else {
        allVehicleList = [...this.data.vehicleList, ...processedVehicleList];
      }

      this.setData({
        vehicleList: allVehicleList,
        vehicleHasMore: res.hasNextPage || false,
        vehicleLoading: false
      });
    }).catch(error => {
      console.error('加载车辆列表失败:', error);
      this.setData({ vehicleLoading: false });
      wx.showToast({
        title: '加载车辆信息失败',
        icon: 'none'
      });
    });
  },

  // 处理车辆数据
  processVehicleData: function(vehicle) {
    // 状态映射 - 使用vehicle_status字典
    try {
      const vehicleStatusDict = util.getDictByNameEn('vehicle_status')[0].children;
      const statusItem = vehicleStatusDict.find(item => item.nameEn === vehicle.status);
      vehicle.statusText = statusItem ? statusItem.nameCn : vehicle.status;
    } catch (error) {
      vehicle.statusText = vehicle.status;
    }

    // 格式化时间
    if (vehicle.createTime) {
      vehicle.formattedCreateTime = dateUtil.formatTime(new Date(vehicle.createTime));
    }
    if (vehicle.updateTime) {
      vehicle.formattedUpdateTime = dateUtil.formatTime(new Date(vehicle.updateTime));
    }

    return vehicle;
  },

  // 进入基本信息编辑模式
  enterBasicEditMode: function() {
    const editData = JSON.parse(JSON.stringify(this.data.residentData));

    // 设置性别选择器索引
    const genderIndex = this.data.genderDict.findIndex(item => item.nameEn === editData.gender);

    // 设置证件类型选择器索引
    const certificateTypeIndex = this.data.certificateTypeDict.findIndex(item => item.nameEn === editData.certificateType);

    // 处理已有标签
    if (editData.tags) {
      const selectedTagsEn = editData.tags.split(',').map(tag => tag.trim());
      editData.selectedTags = this.data.residentTagDict.filter(tag => selectedTagsEn.includes(tag.nameEn));
    } else {
      editData.selectedTags = [];
    }

    this.setData({
      isEditingBasic: true,
      editBasicData: editData,
      editGenderIndex: genderIndex >= 0 ? genderIndex : -1,
      editCertificateTypeIndex: certificateTypeIndex >= 0 ? certificateTypeIndex : -1
    });
  },

  // 取消基本信息编辑
  cancelBasicEdit: function() {
    this.setData({
      isEditingBasic: false,
      editBasicData: JSON.parse(JSON.stringify(this.data.residentData)) // 重置编辑数据
    });
  },

  // 保存基本信息编辑
  saveBasicEdit: function() {
    const editData = this.data.editBasicData;

    // 验证必填字段
    if (!editData.residentName || !editData.phone) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    // 准备提交数据
    const submitData = {
      id: this.data.id,
      residentName: editData.residentName,
      phone: editData.phone,
      gender: editData.gender,
      birthday: editData.birthday,
      certificateType: editData.certificateType,
      idCardNumber: editData.idCardNumber,
      nativePlace: editData.nativePlace || '',
      note: editData.note || '',
      tags: editData.tags || ''
    };

    propertyApi.editResident(submitData).then(res => {
      console.log('编辑居民信息成功:', res);

      // 重新加载居民详情
      this.loadResidentDetail();

      this.setData({
        isEditingBasic: false,
        submitting: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    }).catch(error => {
      console.error('保存居民信息失败:', error);
      this.setData({ submitting: false });
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    });
  },

  // 基本信息编辑输入
  onBasicEditInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const editBasicData = this.data.editBasicData;

    // 更新对应字段
    editBasicData[field] = value;

    this.setData({
      editBasicData: editBasicData
    });
  },

  // 房屋列表加载更多
  loadMoreHouse: function() {
    if (this.data.houseHasMore && !this.data.houseLoading) {
      this.setData({
        housePageNum: this.data.housePageNum + 1
      });
      this.loadHouseList();
    }
  },

  // 车辆列表加载更多
  loadMoreVehicle: function() {
    if (this.data.vehicleHasMore && !this.data.vehicleLoading) {
      this.setData({
        vehiclePageNum: this.data.vehiclePageNum + 1
      });
      this.loadVehicleList();
    }
  },

  // 拨打电话
  makePhoneCall: function() {
    if (!this.data.residentData.phone) {
      wx.showToast({
        title: '手机号不存在',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: this.data.residentData.phone,
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },



  // 查看房屋详情
  viewHouseDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/property/house/detail/index?id=${id}`
    });
  },

  // 查看车辆详情
  viewVehicleDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/vehicle/detail/index?id=${id}`
    });
  },

  // 添加房屋
  addHouse: function() {
    wx.navigateTo({
      url: `/propertyPackage/pages/property/house/add/index?residentId=${this.data.id}`
    });
  },

  // 添加车辆
  addVehicle: function() {
    wx.navigateTo({
      url: `/propertyPackage/pages/property/vehicle/add/index?residentId=${this.data.id}`
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载居民详情
    this.loadResidentDetail();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 编辑房屋信息
  editHouseItem: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/house/edit/index?id=${id}&residentId=${this.data.id}`
    });
  },

  // 编辑车辆信息
  editVehicleItem: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/vehicle/edit/index?id=${id}&residentId=${this.data.id}`
    });
  },

  // 编辑房屋信息
  editHouseItem: function(e) {
    const id = e.currentTarget.dataset.id;
    const houseItem = this.data.houseList.find(item => item.id === id);

    if (!houseItem) {
      wx.showToast({
        title: '房屋信息不存在',
        icon: 'none'
      });
      return;
    }

    // 设置编辑数据
    const editData = JSON.parse(JSON.stringify(houseItem));

    // 设置住户类型选择器索引
    const residentTypeIndex = this.data.residentTypeDict.findIndex(item =>
      item.nameEn === editData.residentType
    );

    // 设置房间类型选择器索引
    const roomTypeIndex = this.data.roomTypeDict.findIndex(item =>
      item.nameEn === editData.roomType
    );

    // 设置状态选择器索引
    const statusIndex = this.data.residentStatusDict.findIndex(item =>
      item.nameEn === editData.status
    );

    this.setData({
      editHouseData: editData,
      editResidentTypeIndex: residentTypeIndex >= 0 ? residentTypeIndex : -1,
      editRoomTypeIndex: roomTypeIndex >= 0 ? roomTypeIndex : -1,
      editStatusIndex: statusIndex >= 0 ? statusIndex : -1,
      showHouseEditModal: true
    });

    // 异步加载楼栋列表并设置反显
    this.loadBuildingListAndSetIndex(editData);
  },

  // 编辑车辆信息
  editVehicleItem: function(e) {
    const id = e.currentTarget.dataset.id;
    const vehicleItem = this.data.vehicleList.find(item => item.id === id);

    if (!vehicleItem) {
      wx.showToast({
        title: '车辆信息不存在',
        icon: 'none'
      });
      return;
    } 
    debugger
    // 跳转到车辆添加页面进行编辑
    wx.navigateTo({
      url: `/profilePackage/pages/profile/vehicle/add/add?mode=edit&id=${id}&source=property&residentId=${this.data.id}`
    });
  },

  // 性别选择
  onGenderChange: function(e) {
    const index = e.detail.value;
    const selectedGender = this.data.genderDict[index];
    this.setData({
      editGenderIndex: index,
      'editBasicData.gender': selectedGender.nameEn,
      'editBasicData.genderText': selectedGender.nameCn
    });
  },

  // 出生日期选择
  onBirthdayChange: function(e) {
    this.setData({
      'editBasicData.birthday': e.detail.value
    });
  },

  // 证件类型选择
  onCertificateTypeChange: function(e) {
    const index = e.detail.value;
    const selectedType = this.data.certificateTypeDict[index];
    this.setData({
      editCertificateTypeIndex: index,
      'editBasicData.certificateType': selectedType.nameEn,
      'editBasicData.certificateTypeText': selectedType.nameCn
    });
  },

  // 显示标签选择弹窗
  showTagSelector: function() {
    // 初始化标签选择状态
    const tagDict = this.data.residentTagDict.map(tag => {
      const isSelected = this.data.editBasicData.selectedTags &&
                        this.data.editBasicData.selectedTags.some(selected => selected.nameEn === tag.nameEn);
      return {
        ...tag,
        selected: isSelected
      };
    });

    this.setData({
      residentTagDict: tagDict,
      showTagModal: true
    });
  },

  // 隐藏标签选择弹窗
  hideTagModal: function() {
    this.setData({
      showTagModal: false
    });
  },

  // 切换标签选择状态
  toggleTag: function(e) {
    const index = e.currentTarget.dataset.index;
    const tagDict = [...this.data.residentTagDict];
    tagDict[index].selected = !tagDict[index].selected;

    this.setData({
      residentTagDict: tagDict
    });
  },

  // 确认标签选择
  confirmTagSelection: function() {
    const selectedTags = this.data.residentTagDict.filter(tag => tag.selected);
    const tagsString = selectedTags.map(tag => tag.nameEn).join(',');

    this.setData({
      'editBasicData.selectedTags': selectedTags,
      'editBasicData.tags': tagsString,
      showTagModal: false
    });
  },

  // 异步加载楼栋列表并设置反显索引
  loadBuildingListAndSetIndex: function(editData) {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: communityId
    };

    communityApi.getPropertyBuildingList(params).then(res => {
      console.log('楼栋列表数据：', res);
      if (res && res.list) {
        // 设置楼栋列表
        this.setData({
          buildingList: res.list
        });

        // 设置楼栋选择器索引
        const buildingIndex = res.list.findIndex(item =>
          String(item.id) === String(editData.buildingId)
        );

        this.setData({
          editBuildingIndex: buildingIndex >= 0 ? buildingIndex : -1
        });

        // 如果有楼栋ID，加载房间列表
        if (editData.buildingId) {
          this.loadRoomListAndSetIndex(editData.buildingId, editData);
        }
      } else {
        wx.showToast({
          title: '获取楼栋列表失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('获取楼栋列表失败：', error);
      wx.showToast({
        title: '获取楼栋列表失败',
        icon: 'none'
      });
    });
  },

  // 加载楼栋列表
  loadBuildingList: function() {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: communityId
    };

    communityApi.getPropertyBuildingList(params).then(res => {
      console.log('楼栋列表数据：', res);
      if (res && res.list) {
        this.setData({
          buildingList: res.list
        });
      } else {
        wx.showToast({
          title: '获取楼栋列表失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('获取楼栋列表失败：', error);
      wx.showToast({
        title: '获取楼栋列表失败',
        icon: 'none'
      });
    });
  },

  // 加载房间列表（包含单元信息）
  loadRoomList: function(buildingId, selectedRoomId = null) {
    const communityId = wx.getStorageSync('selectedCommunity').id;
    const params = {
      pageNum: 1,
      pageSize: 500,
      buildingId: buildingId,
      communityId: communityId
    };

    communityApi.getPropertyRoomList(params).then(res => {
      console.log('房间列表数据：', res);
      if (res && res.list) {
        // 处理房间数据，提取单元信息
        this.processRoomData(res.list);
      } else {
        wx.showToast({
          title: '获取房间列表失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      console.error('获取房间列表失败：', error);
      wx.showToast({
        title: '获取房间列表失败',
        icon: 'none'
      });
    });
  },

  // 处理房间数据，提取单元信息
  processRoomData: function(roomList) {
    // 提取所有唯一的单元号（包括null）
    const unitSet = new Set();
    roomList.forEach(room => {
      unitSet.add(room.unitNumber);
    });

    // 构建单元列表
    const unitList = Array.from(unitSet).map(unitNumber => ({
      unitNumber: unitNumber,
      displayName: unitNumber || '无单元'
    }));

    // 按单元分组房间
    const roomsByUnit = {};
    roomList.forEach(room => {
      const unitKey = room.unitNumber || 'no_unit';
      if (!roomsByUnit[unitKey]) {
        roomsByUnit[unitKey] = [];
      }
      roomsByUnit[unitKey].push(room);
    });

    this.setData({
      unitList: unitList,
      roomList: roomList,
      roomsByUnit: roomsByUnit
    });
  },

  // 楼栋选择
  onBuildingChange: function(e) {
    const index = e.detail.value;
    const selectedBuilding = this.data.buildingList[index];

    this.setData({
      editBuildingIndex: index,
      'editHouseData.buildingId': selectedBuilding.id,
      'editHouseData.buildingNumber': selectedBuilding.buildingNumber,
      editUnitIndex: -1,
      editRoomIndex: -1,
      'editHouseData.unitNumber': null,
      'editHouseData.roomId': '',
      'editHouseData.roomNumber': '',
      'editHouseData.fullAddress': ''
    });

    // 加载对应楼栋的房间列表（包含单元信息）
    this.loadRoomList(selectedBuilding.id);
  },

  // 单元选择
  onUnitChange: function(e) {
    const index = e.detail.value;
    const selectedUnit = this.data.unitList[index];

    // 根据选择的单元过滤房间列表
    let filteredRooms = [];
    if (selectedUnit.unitNumber) {
      // 选择了具体单元，显示该单元下的房间
      filteredRooms = this.data.roomList.filter(room => room.unitNumber === selectedUnit.unitNumber);
    } else {
      // 选择了"无单元"，显示无单元的房间
      filteredRooms = this.data.roomList.filter(room => !room.unitNumber);
    }

    this.setData({
      editUnitIndex: index,
      'editHouseData.unitNumber': selectedUnit.unitNumber,
      editRoomIndex: -1,
      'editHouseData.roomId': '',
      'editHouseData.roomNumber': '',
      'editHouseData.fullAddress': '',
      filteredRoomList: filteredRooms
    });
  },

  // 房间选择
  onRoomChange: function(e) {
    const index = e.detail.value;
    // 使用过滤后的房间列表或完整房间列表
    const roomList = this.data.filteredRoomList || this.data.roomList;
    const selectedRoom = roomList[index];
    const buildingNumber = this.data.editHouseData.buildingNumber;
    const unitNumber = this.data.editHouseData.unitNumber;

    // 生成完整地址
    let fullAddress = buildingNumber;
    if (unitNumber) {
      fullAddress += unitNumber;
    }
    fullAddress += selectedRoom.roomNumber;

    this.setData({
      editRoomIndex: index,
      'editHouseData.roomId': selectedRoom.id,
      'editHouseData.roomNumber': selectedRoom.roomNumber,
      'editHouseData.fullAddress': fullAddress
    });
  },

  // 住户类型选择
  onResidentTypeChange: function(e) {
    const index = e.detail.value;
    const selectedType = this.data.residentTypeDict[index];

    this.setData({
      editResidentTypeIndex: index,
      'editHouseData.residentType': selectedType.nameEn,
      'editHouseData.residentTypeText': selectedType.nameCn
    });
  },

  // 房间户型选择
  onRoomTypeChange: function(e) {
    const index = e.detail.value;
    const selectedType = this.data.roomTypeDict[index];

    this.setData({
      editRoomTypeIndex: index,
      'editHouseData.roomType': selectedType.nameEn,
      'editHouseData.roomTypeText': selectedType.nameCn
    });
  },

  // 状态选择
  onStatusChange: function(e) {
    const index = e.detail.value;
    const selectedStatus = this.data.residentStatusDict[index];

    this.setData({
      editStatusIndex: index,
      'editHouseData.status': selectedStatus.nameEn,
      'editHouseData.statusText': selectedStatus.nameCn
    });
  },

  // 默认房产切换
  onDefaultChange: function(e) {
    this.setData({
      'editHouseData.isDefault': e.detail.value
    });
  },

  // 房产编辑输入
  onHouseEditInput: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    this.setData({
      [`editHouseData.${field}`]: value
    });
  },

  // 隐藏房产编辑弹窗
  hideHouseEditModal: function() {
    this.setData({
      showHouseEditModal: false,
      editHouseData: {},
      houseSubmitting: false
    });
  },

  // 保存房产编辑
  saveHouseEdit: function() {
    const editData = this.data.editHouseData;

    // 验证必填字段
    if (!editData.buildingId || !editData.roomId || !editData.residentType) {
      wx.showToast({
        title: '请填写必填字段',
        icon: 'none'
      });
      return;
    }

    this.setData({
      houseSubmitting: true
    });

    // 准备提交数据
    const submitData = {
      id: editData.id,                           // 房产记录ID
      residentId: this.data.id,                  // 居民ID
      buildingId: editData.buildingId,           // 楼栋ID
      buildingNumber: editData.buildingNumber,   // 楼栋号
      unitNumber: editData.unitNumber || null,   // 单元号
      roomId: editData.roomId,                   // 房间ID
      roomNumber: editData.roomNumber,           // 房间号
      roomType: editData.roomType,               // 房间户型
      residentType: editData.residentType,       // 住户类型
      status: editData.status,                   // 状态
      isDefault: editData.isDefault || false,   // 是否默认
      note: editData.note || ''                  // 备注
    };

    // 调用API保存
    propertyApi.editResidentHouse(submitData).then(res => {
     
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 刷新房屋列表
        this.loadHouseList(true);

        // 关闭弹窗
        this.hideHouseEditModal();
     
    }).catch(error => {
      console.error('保存房产信息失败', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({
        houseSubmitting: false
      });
    });
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})