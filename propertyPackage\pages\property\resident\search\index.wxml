<!--居民搜索页面-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-row">
      <view class="search-input-wrap">
        <view class="search-icon"></view>
        <input class="search-input" type="text" placeholder="请输入居民姓名" value="{{searchResidentName}}" bindinput="onNameInput" />
      </view>
      <view class="search-input-wrap">
        <view class="phone-icon"></view>
        <input class="search-input" type="number" placeholder="请输入手机号" value="{{searchPhone}}" bindinput="onPhoneInput" />
      </view>
    </view>
    <view class="search-actions">
      <button class="search-btn" bindtap="performSearch">搜索</button>
      <button class="clear-btn" bindtap="clearSearch" wx:if="{{searchResidentName || searchPhone}}">清空</button>
    </view>
  </view>

  <!-- 居民列表 -->
  <view class="resident-list">
    <block wx:if="{{residents.length > 0}}">
      <view class="resident-card" wx:for="{{residents}}" wx:key="id" bindtap="viewResidentDetail" data-id="{{item.id}}">
        <view class="resident-content">
          <view class="resident-header">
            <view class="resident-name">{{item.residentName || '未填写'}}</view>
          </view>

          <view class="resident-info">
            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">{{item.phone || '未填写'}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">证件类型</view>
              <view class="info-value">{{item.certificateTypeText || '未填写'}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">证件号</view>
              <view class="info-value">{{item.idCardNumber || '未填写'}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">备注</view>
              <view class="info-value">{{item.note || '无'}}</view>
            </view>
            <view class="info-row">
              <view class="info-label">标签</view>
              <view class="info-value">
                <block wx:if="{{item.tagText}}">
                  <view class="tags">
                    <view class="tag" wx:for="{{item.tagList}}" wx:key="*this" wx:for-item="tag">{{tag}}</view>
                  </view>
                </block>
                <block wx:else>
                  <text class="no-data">无</text>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{residents.length === 0 && !isLoading}}">
      <view class="empty-icon"></view>
      <view class="empty-text">暂无符合条件的居民信息</view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading && residents.length === 0}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !isLoading}}">
    <text bindtap="loadMore">加载更多</text>
  </view>

  <!-- 加载中 -->
  <view class="loading-more" wx:if="{{isLoading && residents.length > 0}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 扫码按钮 -->
  <view class="scan-btn" bindtap="scanQRCode">
    <view class="scan-icon"></view>
    <text>扫码查询</text>
  </view>
</view>
