// pages/payment/history/history.js
const util = require('../../../../utils/util.js')
const payment = require('../../../../api/paymentApi.js')

Page({
  data: {
    isAuthenticated: false,
    userName: '',
    selectedCommunity: '',
    darkMode: false,

    // 筛选相关
    timeLevel: 2, // 时间筛选：2-本月, 3-本季度, 4-本年
    statusFilter: 'all', // 状态筛选：all, wait_pay, settle

    // 缴费记录数据
    paymentRecords: [],

    // 加载状态
    isLoading: true,
    loadingMore: false,
    refreshing: false,
    hasMore: true,

    // 空状态
    showEmpty: false,
    emptyText: '暂无缴费记录',
    emptyActionText: '刷新',

    // 分页
    pageNum: 1,
    pageSize: 10,
    total: 0,

    // 字典数据
    paymentBillStatusDict: [], // 1-1.物业缴费账单状态字典
    paymentDetailStatusDict: [], // 1-2.物业缴费明细状态字典
    paymentTypeDict: [], // 1-3.支付类型字典
    paymentCycleDict: [ // 1-4.支付周期固定值
      { nameEn: 'monthly', nameCn: '月度' },
      { nameEn: 'quarterly', nameCn: '季度' },
      { nameEn: 'yearly', nameCn: '年度' }
    ],

    // 统计数据（暂时保留，等待后续统计接口）
    totalPaid: 0,
    totalUnpaid: 0,
    totalOverdue: 0
  },

  onLoad: function (options) {
    this.checkAuthStatus()
    this.checkCommunitySelection()
    this.loadDictData()
    this.loadPaymentRecords()
  },

  onShow: function () {
    this.checkAuthStatus()
    this.checkCommunitySelection()
  },

  onPullDownRefresh: function () {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    })
    this.loadPaymentRecords(true)
  },

  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMoreRecords()
    }
  },



  // 检查认证状态
  checkAuthStatus: function () {
    const isAuthenticated = util.checkAuthentication()
    const userName = wx.getStorageSync('userName') || ''

    this.setData({
      isAuthenticated,
      userName
    })
  },

  // 检查社区选择
  checkCommunitySelection: function () {
    const selectedCommunity = wx.getStorageSync('selectedCommunity') || ''

    this.setData({
      selectedCommunity
    })
  },

  // 加载字典数据
  loadDictData: function() {
    try {
      // 1-1.物业缴费账单状态字典
      const paymentBillStatusDict = util.getDictByNameEn('property_payment_bill_status')[0].children || [];

      // 1-2.物业缴费明细状态字典
      const paymentDetailStatusDict = util.getDictByNameEn('property_payment_detail_status')[0].children || [];

      // 1-3.支付类型字典
      const paymentTypeDict = util.getDictByNameEn('pay_type')[0].children || [];

      this.setData({
        paymentBillStatusDict: paymentBillStatusDict,
        paymentDetailStatusDict: paymentDetailStatusDict,
        paymentTypeDict: paymentTypeDict
      });

      console.log('1-1.缴费账单状态字典加载完成:', paymentBillStatusDict);
      console.log('1-2.缴费明细状态字典加载完成:', paymentDetailStatusDict);
      console.log('1-3.支付类型字典加载完成:', paymentTypeDict);
    } catch (error) {
      console.error('字典数据加载失败:', error);
      this.setData({
        paymentBillStatusDict: [],
        paymentDetailStatusDict: [],
        paymentTypeDict: []
      });
    }
  },

  // 加载缴费记录
  loadPaymentRecords: function (isPullDown = false) {
    // 如果是下拉刷新，重置分页
    if (isPullDown) {
      this.setData({
        pageNum: 1,
        paymentRecords: [],
        hasMore: true,
        refreshing: true
      });
    }

    this.setData({
      isLoading: !isPullDown, // 下拉刷新时不显示loading
      loadingMore: !isPullDown && this.data.pageNum > 1
    });

    // 获取小区ID
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      this.setData({
        isLoading: false,
        refreshing: false,
        loadingMore: false,
        showEmpty: true,
        emptyText: '请先选择小区'
      });
      if (isPullDown) {
        wx.stopPullDownRefresh();
      }
      return;
    }

    // 构建请求参数
    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: selectedCommunity.id,
      timeLevel: this.data.timeLevel
    };

    // 添加状态筛选
    if (this.data.statusFilter !== 'all') {
      params.status = this.data.statusFilter;
    }

    console.log('加载缴费记录，参数:', params);

    payment.queryMyPaymentList(params).then(res => {
      console.log('缴费记录API响应:', res);

      if (res && res.list) {
        const newRecords = res.list || [];
        const allRecords = this.data.pageNum === 1 ? newRecords : [...this.data.paymentRecords, ...newRecords];

        this.setData({
          paymentRecords: allRecords,
          total: res.total || 0,
          hasMore: allRecords.length < (res.total || 0),
          isLoading: false,
          refreshing: false,
          loadingMore: false,
          showEmpty: allRecords.length === 0,
          emptyText: allRecords.length === 0 ? '暂无缴费记录' : ''
        });
      } else {
        this.setData({
          isLoading: false,
          refreshing: false,
          loadingMore: false,
          showEmpty: true,
          emptyText: '暂无缴费记录'
        });
      }

      if (isPullDown) {
        wx.stopPullDownRefresh();
      }
    }).catch(error => {
      console.error('加载缴费记录失败:', error);
      this.setData({
        isLoading: false,
        refreshing: false,
        loadingMore: false,
        showEmpty: true,
        emptyText: '加载失败，请重试'
      });

      if (isPullDown) {
        wx.stopPullDownRefresh();
      }

      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  // 加载更多记录
  loadMoreRecords: function () {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }

    this.setData({
      pageNum: this.data.pageNum + 1
    });

    this.loadPaymentRecords(false);
  },



  // 工具方法

  // 获取缴费名称（从paymentItemSnapshot解析）
  getPaymentName: function(record) {
    if (record.paymentItemSnapshot) {
      try {
        const snapshot = JSON.parse(record.paymentItemSnapshot);
        return snapshot.paymentItemName || '未知缴费项目';
      } catch (error) {
        console.error('解析paymentItemSnapshot失败:', error);
        return '未知缴费项目';
      }
    }
    return '未知缴费项目';
  },

  // 获取支付周期（从paymentItemSnapshot解析）
  getPaymentCycle: function(record) {
    if (record.paymentItemSnapshot) {
      try {
        const snapshot = JSON.parse(record.paymentItemSnapshot);
        const billingCycle = snapshot.billingCycle;

        // 使用固定的支付周期字典
        const cycleDict = this.data.paymentCycleDict;
        const cycleItem = cycleDict.find(item => item.nameEn === billingCycle);
        return cycleItem ? cycleItem.nameCn : (billingCycle || '未知周期');
      } catch (error) {
        console.error('解析paymentItemSnapshot失败:', error);
        return '未知周期';
      }
    }
    return '未知周期';
  },

  // 获取状态文本（使用字典）
  getStatusText: function(status) {
    const statusDict = this.data.paymentBillStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === status);
    return statusItem ? statusItem.nameCn : status;
  },

  // 获取支付类型文本（使用字典）
  getPaymentTypeText: function(payType) {
    if (!payType) return '';
    const typeDict = this.data.paymentTypeDict;
    const typeItem = typeDict.find(item => item.nameEn === payType);
    return typeItem ? typeItem.nameCn : payType;
  },

  // 格式化金额
  formatAmount: function(amount) {
    if (!amount && amount !== 0) return '0.00';
    return parseFloat(amount).toFixed(2);
  },

  // 格式化日期
  formatDate: function(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 切换时间筛选
  changeTimeFilter: function(e) {
    const timeLevel = parseInt(e.currentTarget.dataset.level);
    this.setData({
      timeLevel: timeLevel,
      pageNum: 1,
      paymentRecords: [],
      hasMore: true
    });
    this.loadPaymentRecords(false);
  },

  // 切换状态筛选
  changeStatusFilter: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      statusFilter: filter,
      pageNum: 1,
      paymentRecords: [],
      hasMore: true
    });
    this.loadPaymentRecords(false);
  },

  // 查看缴费详情
  viewPaymentDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const record = this.data.paymentRecords.find(r => r.id === id);

    if (record) {
      // 存储详情数据
      wx.setStorageSync('paymentDetail', record);

      // 跳转到详情页
      wx.navigateTo({
        url: '/servicePackage/pages/payment/detail/detail'
      });
    }
  },

  // 立即缴费
  payNow: function(e) {
    const id = e.currentTarget.dataset.id;
    const record = this.data.paymentRecords.find(r => r.id === id);

    if (record && record.status === 'wait_pay') {
      // 存储缴费数据
      wx.setStorageSync('paymentData', record);

      // 跳转到缴费页
      wx.navigateTo({
        url: '/servicePackage/pages/payment/pay/pay'
      });
    }
  },

  // 空状态操作
  handleEmptyAction: function() {
    this.setData({
      pageNum: 1,
      paymentRecords: [],
      hasMore: true
    });
    this.loadPaymentRecords(false);
  },

  // 切换时间筛选
  changeTimeFilter: function (e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      timeFilter: filter
    })
    this.updateFilteredRecords()
  },

  // 切换类型筛选
  changeTypeFilter: function (e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      typeFilter: filter
    })
    this.updateFilteredRecords()
  },

  // 切换状态筛选
  changeStatusFilter: function (e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({
      statusFilter: filter
    })
    this.updateFilteredRecords()
  },

  // 更新筛选后的记录
  updateFilteredRecords: function () {
    const filtered = this.filterRecords(this.data.paymentRecords)
    this.setData({
      filteredRecords: filtered,
      showEmpty: filtered.length === 0
    })

    // 更新空状态文本
    this.updateEmptyStateText()
  },

  // 更新空状态文本
  updateEmptyStateText: function () {
    let emptyText = '暂无缴费记录'
    let actionText = '刷新'

    if (this.data.statusFilter === 'unpaid') {
      emptyText = '暂无待支付的费用'
      actionText = '查看全部记录'
    } else if (this.data.statusFilter === 'overdue') {
      emptyText = '太好了！没有逾期费用'
      actionText = '查看全部记录'
    } else if (this.data.timeFilter !== 'all' || this.data.typeFilter !== 'all') {
      emptyText = '没有符合条件的缴费记录'
      actionText = '重置筛选'
    }

    this.setData({
      emptyText,
      emptyActionText: actionText
    })
  },

  // 重置筛选
  resetFilters: function () {
    this.setData({
      timeFilter: 'all',
      typeFilter: 'all',
      statusFilter: 'all'
    })
    this.updateFilteredRecords()
  },

  // 刷新记录
  refreshRecords: function () {
    this.setData({
      page: 1,
      hasMore: true
    })
    this.loadPaymentRecords()
  },

  // 空状态操作
  handleEmptyAction: function () {
    if (this.data.timeFilter !== 'all' || this.data.typeFilter !== 'all' || this.data.statusFilter !== 'all') {
      this.resetFilters()
    } else {
      this.refreshRecords()
    }
  },

  // 查看缴费详情
  viewPaymentDetail: function (e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/servicePackage/pages/payment/detail/detail?id=${id}`
    })
  },

  // 查看电子发票
  downloadInvoice: function (e) {
    const id = e.currentTarget.dataset.id

    // 跳转到发票预览页面
    wx.navigateTo({
      url: `/servicePackage/pages/payment/invoice/invoice?id=${id}`
    })
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  },

  // 跳转到缴费设置页面
  navigateToSettings: function () {
    wx.navigateTo({
      url: '/servicePackage/pages/payment/settings/settings'
    })
  },

  // 跳转到缴费分析页面
  navigateToAnalysis: function () {
    wx.navigateTo({
      url: '/servicePackage/pages/payment/analysis/analysis'
    })
  }
})
