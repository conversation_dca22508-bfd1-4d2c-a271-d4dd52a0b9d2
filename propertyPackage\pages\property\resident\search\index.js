// pages/property/resident/search/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('@/api/propertyApi.js')
const dateUtil = require('@/utils/dateUtil.js')
Page({
  data: {
    // 搜索相关
    searchResidentName: '', // 搜索姓名
    searchPhone: '', // 搜索手机号

    // 分页相关
    pageNum: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    isLoading: false,
    isRefreshing: false,

    // 居民列表
    residents: [],

    // 字典数据
    residentTypeDict: [], // 居民类型字典
    genderDict: [], // 性别字典
    certificateTypeDict: [], // 证件类型字典
    residentStatusDict: [], // 居民状态字典

    // 触摸滑动相关
    startX: 0,
    moveX: 0
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民查询'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载居民列表
    this.loadResidents();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 居民类型字典
      const residentTypeDict = util.getDictByNameEn('resident_type')[0].children;
      // 性别字典 - 注意文档中写错了，应该是gender不是resident_type
      const genderDict = util.getDictByNameEn('gender')[0].children;
      // 证件类型字典
      const certificateTypeDict = util.getDictByNameEn('certificate_type')[0].children;
      // 居民状态字典
      const residentStatusDict = util.getDictByNameEn('resident_status')[0].children;

      this.setData({
        residentTypeDict,
        genderDict,
        certificateTypeDict,
        residentStatusDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载居民列表
  loadResidents: function(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        pageNum: 1,
        residents: [],
        hasMore: true,
        isRefreshing: true
      });
    } else {
      this.setData({ isLoading: true });
    }

    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      phone: this.data.searchPhone,
      residentName: this.data.searchResidentName,
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    console.log('加载居民列表参数:', params);

    propertyApi.propertyGetResidentList(params).then(res => {
      console.log('居民列表响应:', res);

      const newResidents = res.list || [];
      // 处理居民数据，添加字典映射
      const processedResidents = newResidents.map(resident => this.processResidentData(resident));

      let allResidents = [];
      if (isRefresh || this.data.pageNum === 1) {
        allResidents = processedResidents;
      } else {
        allResidents = [...this.data.residents, ...processedResidents];
      }

      this.setData({
        residents: allResidents,
        total: res.total || 0,
        hasMore: res.hasNextPage || false,
        isLoading: false,
        isRefreshing: false
      });

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }
    }).catch(error => {
      console.error('加载居民列表失败:', error);
      this.setData({
        isLoading: false,
        isRefreshing: false
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });

      if (isRefresh) {
        wx.stopPullDownRefresh();
      }
    });
  },

  // 处理居民数据，添加字典映射
  processResidentData: function(resident) {
    // 性别映射
    const genderItem = this.data.genderDict.find(item => item.nameEn === resident.gender);
    resident.genderText = genderItem ? genderItem.nameCn : resident.gender;

    // 证件类型映射
    const certificateItem = this.data.certificateTypeDict.find(item => item.nameEn === resident.certificateType);
    resident.certificateTypeText = certificateItem ? certificateItem.nameCn : resident.certificateType;

    // 脱敏处理身份证号
    if (resident.idCardNumber) {
      resident.maskedIdCard = resident.idCardNumber.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
    }

    // 脱敏处理手机号
    if (resident.phone) {
      resident.maskedPhone = resident.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }

    // 格式化创建时间
    if (resident.createTime) {
      resident.formattedCreateTime = dateUtil.formatTime(new Date(resident.createTime));
    }

    return resident;
  },

  // 搜索姓名输入
  onNameInput: function(e) {
    const searchResidentName = e.detail.value;
    this.setData({ searchResidentName });
    this.debounceSearch();
  },

  // 搜索手机号输入
  onPhoneInput: function(e) {
    const searchPhone = e.detail.value;
    this.setData({ searchPhone });
    this.debounceSearch();
  },

  // 防抖搜索
  debounceSearch: function() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    this.searchTimer = setTimeout(() => {
      this.loadResidents(true);
    }, 500);
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchResidentName: '',
      searchPhone: ''
    });
    this.loadResidents(true);
  },

  // 执行搜索
  performSearch: function() {
    this.loadResidents(true);
  },

  // 加载更多
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.setData({
        pageNum: this.data.pageNum + 1
      });
      this.loadResidents();
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadResidents(true);
  },

  // 上拉加载更多
  onReachBottom: function() {
    this.loadMore();
  },

  // 查看居民详情
  viewResidentDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/detail/index?id=${id}`
    });
  },

  // 扫码查询
  scanQRCode: function() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res);
        if (res.result) {
          wx.navigateTo({
            url: `/propertyPackage/pages/property/resident/detail/index?id=${res.result}`
          });
        }
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})
