// pages/payment/detail/detail.js
const util = require('../../../../utils/util.js')
const paymentApi = require('../../../../api/paymentApi.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,

    // 缴费详情数据
    paymentId: '',
    paymentDetail: null,

    // 账单明细是否展开
    billDetailsExpanded: false,

    // 发票状态
    invoiceAvailable: false,

    // 分享选项是否显示
    showShareOptions: false,

    // 字典数据（复用列表页的逻辑）
    paymentBillStatusDict: [], // 物业缴费账单状态字典
    paymentDetailStatusDict: [], // 物业缴费明细状态字典
    paymentTypeDict: [], // 支付类型字典
    paymentCycleDict: [ // 支付周期固定值
      { nameEn: 'monthly', nameCn: '月度' },
      { nameEn: 'quarterly', nameCn: '季度' },
      { nameEn: 'yearly', nameCn: '年度' }
    ]
  },

  onLoad: function (options) {
    // 加载字典数据
    this.loadDictData()

    // 获取缴费ID
    if (options.id) {
      this.setData({
        paymentId: options.id
      })
      this.loadPaymentDetail(options.id)
    } else {
      wx.showToast({
        title: '缴费ID无效',
        icon: 'error'
      })
      this.setData({
        isLoading: false
      })

      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载字典数据（复用列表页逻辑）
  loadDictData: function() {
    try {
      // 1-1.物业缴费账单状态字典
      const paymentBillStatusDict = util.getDictByNameEn('property_payment_bill_status')[0].children || [];

      // 1-2.物业缴费明细状态字典
      const paymentDetailStatusDict = util.getDictByNameEn('property_payment_detail_status')[0].children || [];

      // 1-3.支付类型字典
      const paymentTypeDict = util.getDictByNameEn('pay_type')[0].children || [];

      this.setData({
        paymentBillStatusDict: paymentBillStatusDict,
        paymentDetailStatusDict: paymentDetailStatusDict,
        paymentTypeDict: paymentTypeDict
      });

      console.log('详情页字典数据加载完成');
    } catch (error) {
      console.error('详情页字典数据加载失败:', error);
      this.setData({
        paymentBillStatusDict: [],
        paymentDetailStatusDict: [],
        paymentTypeDict: []
      });
    }
  },



  // 加载缴费详情
  loadPaymentDetail: function (id) {
    this.setData({
      isLoading: true
    })

    console.log('加载缴费详情，ID:', id);

    paymentApi.getPaymentDetail(id).then(res => {
      console.log('缴费详情API响应:', res);

      if (res) {
        // 预处理详情数据，添加显示字段
        const processedDetail = this.processDetailData(res);

        this.setData({
          paymentDetail: processedDetail,
          invoiceAvailable: res.status === 'settle', // 已结算的可以查看发票
          isLoading: false
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
      }
    }).catch(error => {
      console.error('加载缴费详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
    });
  },

  // 处理详情数据，添加显示字段（复用列表页逻辑）
  processDetailData: function(detail) {
    return {
      ...detail,
      // 基础显示字段
      displayName: this.getPaymentName(detail),
      displayStatus: this.getStatusText(detail.status),
      displayCycle: this.getPaymentCycle(detail),
      displayPayType: this.getPaymentTypeText(detail.payType),
      displayAmount: this.formatAmount(detail.payAmount),
      displayTotalAmount: this.formatAmount(detail.totalAmount),
      displayDiscountAmount: this.formatAmount(detail.discountAmount),
      displayUnitPrice: this.formatAmount(detail.unitPrice),
      displayPayTime: this.formatDate(detail.payTime),
      displayBillDate: this.formatDate(detail.billDate),
      displayCreateTime: this.formatDate(detail.createTime),

      // 计算字段
      displayQuantity: detail.quantity || 0,
      displayUnit: detail.unit || '',

      // 从paymentItemSnapshot解析的详细信息
      itemDetails: this.parsePaymentItemSnapshot(detail.paymentItemSnapshot)
    };
  },

  // 解析paymentItemSnapshot获取详细信息
  parsePaymentItemSnapshot: function(snapshotStr) {
    if (!snapshotStr) return null;

    try {
      const snapshot = JSON.parse(snapshotStr);
      return {
        ...snapshot,
        displayUnitPrice: this.formatAmount(snapshot.unitPrice),
        displayCycle: this.getCycleText(snapshot.billingCycle)
      };
    } catch (error) {
      console.error('解析paymentItemSnapshot失败:', error);
      return null;
    }
  },

  // 工具方法（复用列表页逻辑）

  // 获取缴费名称
  getPaymentName: function(record) {
    if (record.paymentItemSnapshot) {
      try {
        const snapshot = JSON.parse(record.paymentItemSnapshot);
        return snapshot.paymentItemName || '未知缴费项目';
      } catch (error) {
        console.error('解析paymentItemSnapshot失败:', error);
        return '未知缴费项目';
      }
    }
    return '未知缴费项目';
  },

  // 获取支付周期
  getPaymentCycle: function(record) {
    if (record.paymentItemSnapshot) {
      try {
        const snapshot = JSON.parse(record.paymentItemSnapshot);
        const billingCycle = snapshot.billingCycle;
        return this.getCycleText(billingCycle);
      } catch (error) {
        console.error('解析paymentItemSnapshot失败:', error);
        return '未知周期';
      }
    }
    return '未知周期';
  },

  // 获取周期文本
  getCycleText: function(billingCycle) {
    const cycleDict = this.data.paymentCycleDict;
    const cycleItem = cycleDict.find(item => item.nameEn === billingCycle);
    return cycleItem ? cycleItem.nameCn : (billingCycle || '未知周期');
  },

  // 获取状态文本
  getStatusText: function(status) {
    const statusDict = this.data.paymentBillStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === status);
    return statusItem ? statusItem.nameCn : status;
  },

  // 获取支付类型文本
  getPaymentTypeText: function(payType) {
    if (!payType) return '';
    const typeDict = this.data.paymentTypeDict;
    const typeItem = typeDict.find(item => item.nameEn === payType);
    return typeItem ? typeItem.nameCn : payType;
  },

  // 格式化金额
  formatAmount: function(amount) {
    if (!amount && amount !== 0) return '0.00';
    return parseFloat(amount).toFixed(2);
  },

  // 格式化日期
  formatDate: function(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 事件处理方法

  // 切换账单明细展开状态
  toggleBillDetails: function() {
    this.setData({
      billDetailsExpanded: !this.data.billDetailsExpanded
    });
  },

  // 立即缴费
  payNow: function() {
    const detail = this.data.paymentDetail;
    if (detail && detail.status === 'wait_pay') {
      // 存储缴费数据
      wx.setStorageSync('paymentData', detail);

      // 跳转到缴费页
      wx.navigateTo({
        url: '/servicePackage/pages/payment/pay/pay'
      });
    }
  },

  // 查看发票
  viewInvoice: function() {
    const detail = this.data.paymentDetail;
    if (detail && this.data.invoiceAvailable) {
      wx.navigateTo({
        url: `/servicePackage/pages/payment/invoice/invoice?id=${detail.id}`
      });
    }
  },

  // 联系客服
  contactService: function() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享缴费凭证
  sharePayment: function() {
    this.setData({
      showShareOptions: !this.data.showShareOptions
    });
  },

  // 分享到微信
  shareToWeChat: function() {
    // 微信小程序内置分享功能
    wx.showShareMenu({
      withShareTicket: true
    });
    this.setData({
      showShareOptions: false
    });
  },

  // 保存到相册
  saveToAlbum: function() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
    this.setData({
      showShareOptions: false
    });
  },

  // 复制链接
  copyLink: function() {
    const detail = this.data.paymentDetail;
    if (detail) {
      wx.setClipboardData({
        data: `缴费详情：${detail.displayName}，金额：¥${detail.displayAmount}`,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        }
      });
    }
    this.setData({
      showShareOptions: false
    });
  },

  // 微信分享配置
  onShareAppMessage: function () {
    const detail = this.data.paymentDetail;
    return {
      title: `${detail.displayName}缴费凭证`,
      path: `/servicePackage/pages/payment/detail/detail?id=${detail.id}`,
      imageUrl: '/images/share-payment.png' // 默认分享图片
    };
  },

  // 切换账单明细展开/收起
  toggleBillDetails: function () {
    this.setData({
      billDetailsExpanded: !this.data.billDetailsExpanded
    })
  },

  // 查看发票
  viewInvoice: function () {
    if (!this.data.invoiceAvailable) {
      wx.showToast({
        title: '发票暂不可用',
        icon: 'none'
      })
      return
    }

    const id = this.data.paymentDetail.id
    wx.navigateTo({
      url: `/servicePackage/pages/payment/invoice/invoice?id=${id}`
    })
  },

  // 显示分享选项
  showShareOptions: function () {
    this.setData({
      showShareOptions: true
    })
  },

  // 隐藏分享选项
  hideShareOptions: function () {
    this.setData({
      showShareOptions: false
    })
  },

  // 分享到微信
  shareToWechat: function () {
    this.hideShareOptions()
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 保存截图
  saveScreenshot: function () {
    this.hideShareOptions()
    wx.showLoading({
      title: '生成截图中...',
      mask: true
    })

    // 模拟截图生成过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    }, 1500)
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack()
  },

  // 跳转到缴费设置页面
  navigateToSettings: function () {
    wx.navigateTo({
      url: '/servicePackage/pages/payment/settings/settings'
    })
  },

  // 跳转到缴费分析页面
  navigateToAnalysis: function () {
    wx.navigateTo({
      url: '/servicePackage/pages/payment/analysis/analysis'
    })
  },

  // 分享给好友
  onShareAppMessage: function () {
    const detail = this.data.paymentDetail
    return {
      title: `${detail.title}缴费凭证`,
      path: `/servicePackage/pages/payment/detail/detail?id=${detail.id}`,
      imageUrl: '/images/share-payment.png' // 默认分享图片
    }
  }
})
