/* 居民详情页样式 */
.container {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding-bottom: 120rpx;
}

/* 顶部操作栏 */
.action-bar {
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: flex-end;
  background: #FFFFFF;
}

.btn-edit, .btn-cancel, .btn-save, .btn-edit-item {
  padding: 10rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  line-height: 1;
}

.btn-edit {
  background: #E3F2FD;
  color: #1976D2;
}

.btn-cancel {
  background: #F2F2F7;
  color: #8E8E93;
  margin-right: 16rpx;
}

.btn-save {
  background: #1976D2;
  color: #FFFFFF;
}

.btn-save[disabled] {
  opacity: 0.5;
}

.btn-edit-item {
  background: #E8F5E8;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.btn-call {
  background: #E3F2FD;
  color: #1976D2;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: #FFFFFF;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.tab {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #8E8E93;
  position: relative;
}

.tab.active {
  color: #FF8C00;
  font-weight: 500;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #FF8C00;
  border-radius: 2rpx;
}

/* 标签页内容 */
.tab-content {
  padding: 24rpx 0;
}

/* 详情卡片样式 */
.detail-card {
  background: #FFFFFF;
  border-radius: 28rpx;
  margin: 0 32rpx 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
}

.card-actions {
  display: flex;
  gap: 12rpx;
}

.card-action {
  font-size: 28rpx;
  color: #FF8C00;
}

/* 居民头部信息 */
.resident-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.resident-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.resident-badges {
  display: flex;
}

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  padding: 0 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-left: 16rpx;
}

.badge.owner {
  background: rgba(255, 140, 0, 0.1);
  color: #FF8C00;
}

.badge.tenant {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.badge.verified {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.badge.unverified {
  background: rgba(142, 142, 147, 0.1);
  color: #8E8E93;
}

/* 信息列表样式 */
.info-list {
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #8E8E93;
  flex-shrink: 0;
  line-height: 1.5;
  padding-top: 16rpx;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #000000;
}

/* 编辑模式输入框 */
.info-input {
  flex: 1;
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #000000;
}

.radio-group {
  display: flex;
}

.radio {
  margin-right: 32rpx;
  font-size: 28rpx;
  color: #000000;
}

.picker-value {
  height: 72rpx;
  background: #F2F2F7;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #000000;
  display: flex;
  align-items: center;
}

/* 房屋列表样式 */
.house-list {
  margin-top: 16rpx;
}

.house-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.house-item:last-child {
  border-bottom: none;
}

.house-info {
  flex: 1;
}

.item-actions {
  margin-left: 16rpx;
}

/* 编辑按钮区域样式 */
.edit-actions {
  padding: 24rpx 32rpx;
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

/* 主要编辑按钮样式 */
.btn-edit-main, .btn-cancel-main, .btn-confirm-main {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  min-width: 120rpx;
  line-height: 1.2;
}

.btn-edit-main {
  background: #E3F2FD;
  color: #1976D2;
}

.btn-cancel-main {
  background: #F44336;
  color: #FFFFFF;
}

.btn-confirm-main {
  background: #1976D2;
  color: #FFFFFF;
}

.btn-confirm-main[disabled] {
  opacity: 0.5;
}

/* 列表容器样式 */
.house-list, .vehicle-list {
  padding: 0 32rpx;
}

/* 列表项卡片样式 */
.house-item-card, .vehicle-item-card {
  background: #FFFFFF;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.item-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #F8F9FA;
  border-bottom: 1rpx solid #E9ECEF;
}

.item-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.item-card-actions {
  display: flex;
  align-items: center;
}

/* Card内编辑按钮样式 */
.btn-edit-card {
  padding: 8rpx 16rpx;
  background: #E8F5E8;
  color: #4CAF50;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1.2;
  min-width: 60rpx;
}

.item-card-content {
  padding: 20rpx;
}

.item-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.item-info-row:last-child {
  border-bottom: none;
}

.item-info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.item-info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 标签样式 */
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  justify-content: flex-start;
}

.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2rpx 12rpx;
  background-color: #E3F2FD;
  color: #1976D2;
  border-radius: 12rpx;
  font-size: 24rpx;
  line-height: 1.2;
}

.no-data {
  color: #999;
  font-size: 28rpx;
}

/* 表单输入样式 */
.info-input, .info-textarea {
  background: #FFFFFF;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.info-textarea {
  min-height: 120rpx;
  resize: none;
}

.info-picker {
  background: #FFFFFF;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.picker-display {
  font-size: 28rpx;
  color: #333;
}

.picker-display:empty::before {
  content: attr(placeholder);
  color: #999;
}

/* 标签编辑容器 */
.tag-edit-container {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  min-height: 40rpx;
}

.btn-select-tags {
  padding: 8rpx 16rpx;
  background: #E3F2FD;
  color: #1976D2;
  border: 1rpx solid #BBDEFB;
  border-radius: 6rpx;
  font-size: 24rpx;
  align-self: flex-start;
  min-width: 120rpx;
}

/* 标签弹窗样式 */
.tag-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}

.tag-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  max-height: 80%;
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  cursor: pointer;
}

.modal-body {
  max-height: 500rpx;
  overflow-y: auto;
  padding: 24rpx 32rpx;
}

.tag-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  border-radius: 8rpx;
  transition: background-color 0.3s;
}

.tag-item:hover {
  background: #F5F5F5;
}

.tag-item.selected {
  background: #E3F2FD;
}

.tag-checkbox {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #CCCCCC;
  border-radius: 4rpx;
  background: #FFFFFF;
  position: relative;
}

.checkbox.checked {
  background: #1976D2;
  border-color: #1976D2;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 4rpx;
  width: 12rpx;
  height: 20rpx;
  border: 2rpx solid #FFFFFF;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

.tag-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #E0E0E0;
}

.modal-footer .btn-cancel,
.modal-footer .btn-confirm {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.modal-footer .btn-cancel {
  background: #F44336;
  color: #FFFFFF;
}

.modal-footer .btn-confirm {
  background: #1976D2;
  color: #FFFFFF;
}

/* 房产编辑弹窗样式 */
.house-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}

.house-edit-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.house-edit-content {
  width: 90%;
  max-width: 700rpx;
  max-height: 85%;
}

.house-edit-form {
  padding: 0;
}

.form-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #E3F2FD;
}

.form-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.form-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  padding-top: 16rpx;
  line-height: 1.5;
}

.form-label.required::before {
  content: '*';
  color: #F44336;
  margin-right: 4rpx;
}

.form-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 8rpx;
  line-height: 1.4;
}

.auto-address {
  color: #999;
  font-style: italic;
}

.form-picker {
  flex: 1;
  background: #FFFFFF;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 16rpx;
}

.form-picker .picker-display {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.form-textarea {
  flex: 1;
  background: #FFFFFF;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
  resize: none;
  line-height: 1.4;
}

.form-switch {
  transform: scale(0.8);
}

/* 房产编辑弹窗样式 */
.house-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
}

.house-edit-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.house-edit-content {
  width: 90%;
  max-width: 700rpx;
  max-height: 85%;
}

.house-edit-form {
  padding: 0;
}

.form-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #E3F2FD;
}

.form-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.form-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  padding-top: 16rpx;
  line-height: 1.5;
}

.form-label.required::before {
  content: '*';
  color: #F44336;
  margin-right: 4rpx;
}

.form-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 8rpx;
  line-height: 1.4;
}

.auto-address {
  color: #999;
  font-style: italic;
}

.form-picker {
  flex: 1;
  background: #FFFFFF;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 16rpx;
}

.form-picker .picker-display {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.form-picker .picker-display:empty::before {
  content: attr(placeholder);
  color: #999;
}

.form-textarea {
  flex: 1;
  background: #FFFFFF;
  border: 1rpx solid #E0E0E0;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
  resize: none;
  line-height: 1.4;
}

.house-address {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8rpx;
}

.house-details {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
  display: flex;
  justify-content: space-between;
}

.house-relation {
  color: #FF8C00;
}

.house-date {
  font-size: 24rpx;
  color: #8E8E93;
}

.house-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 车辆列表样式 */
.vehicle-list {
  margin-top: 16rpx;
}

.vehicle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.vehicle-item:last-child {
  border-bottom: none;
}

.vehicle-info {
  flex: 1;
}

.vehicle-plate {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8rpx;
}

.vehicle-details {
  font-size: 26rpx;
  color: #8E8E93;
  margin-bottom: 8rpx;
}

.vehicle-parking {
  font-size: 24rpx;
  color: #8E8E93;
}

.vehicle-arrow {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 操作记录列表样式 */
.record-list {
  margin-top: 16rpx;
}

.record-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24rpx 24rpx;
}

.record-icon.identity {
  background-color: rgba(255, 140, 0, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF8C00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='16' rx='2'%3E%3C/rect%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Ccircle cx='12' cy='11' r='3'%3E%3C/circle%3E%3Cpath d='M17 18.5c-1.4-1-3.1-1.5-5-1.5s-3.6.5-5 1.5'%3E%3C/path%3E%3C/svg%3E");
}

.record-icon.house {
  background-color: rgba(255, 69, 58, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF453A' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z'%3E%3C/path%3E%3Cpolyline points='9 22 9 12 15 12 15 22'%3E%3C/polyline%3E%3C/svg%3E");
}

.record-icon.vehicle {
  background-color: rgba(52, 199, 89, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='1' y='6' width='22' height='12' rx='2'%3E%3C/rect%3E%3Cpath d='M4 12h16'%3E%3C/path%3E%3Cpath d='M7 18v2'%3E%3C/path%3E%3Cpath d='M17 18v2'%3E%3C/path%3E%3Cpath d='M7 6v2'%3E%3C/path%3E%3Cpath d='M17 6v2'%3E%3C/path%3E%3C/svg%3E");
}

.record-icon.contact {
  background-color: rgba(0, 122, 255, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23007AFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E");
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 28rpx;
  color: #000000;
  margin-bottom: 8rpx;
}

.record-details {
  font-size: 24rpx;
  color: #8E8E93;
  display: flex;
  justify-content: space-between;
}

.record-operator {
  color: #8E8E93;
}

/* 空状态样式 */
.empty-state {
  padding: 48rpx 0;
  text-align: center;
}

.empty-icon {
  width: 96rpx;
  height: 96rpx;
  margin: 0 auto 16rpx;
  background: rgba(142, 142, 147, 0.1);
  border-radius: 48rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 36rpx 36rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.btn-contact, .btn-notify {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-contact {
  background: #FFFFFF;
  color: #FF8C00;
  border: 1rpx solid rgba(255, 140, 0, 0.2);
  margin-right: 24rpx;
}

.btn-notify {
  background: #FF8C00;
  color: #FFFFFF;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 28rpx;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
}

.modal-body {
  padding: 32rpx;
}

/* 联系方式弹窗 */
.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24rpx 24rpx;
}

.contact-icon.phone {
  background-color: rgba(52, 199, 89, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2334C759' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z'%3E%3C/path%3E%3C/svg%3E");
}

.contact-icon.sms {
  background-color: rgba(0, 122, 255, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23007AFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z'%3E%3C/path%3E%3C/svg%3E");
}

.contact-text {
  font-size: 30rpx;
  color: #000000;
}

/* 发送通知弹窗 */
.notify-content {
  width: 100%;
  height: 240rpx;
  background: #F2F2F7;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #000000;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid rgba(60, 60, 67, 0.1);
}

.modal-footer button {
  flex: 1;
  height: 100rpx;
  border-radius: 0;
  font-size: 32rpx;
  background: transparent;
}

.modal-footer button::after {
  border: none;
}

.modal-footer .btn-cancel {
  color: #8E8E93;
  background: transparent;
}

.modal-footer .btn-confirm {
  color: #FF8C00;
  font-weight: 600;
  background: transparent;
}

.modal-footer .btn-confirm[disabled] {
  opacity: 0.5;
}